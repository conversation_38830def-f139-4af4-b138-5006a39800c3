"""
Test script for 5sim integration with Gmail phone verification
This script tests the 5sim API integration without running the full Gmail automation
"""

import logging
import json
import sys
import os

# Add current directory to path for imports
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, current_dir)

try:
    from fivesim_integration import FiveSimManager, FiveSimClient, load_fivesim_config, save_fivesim_config
    print("✓ 5sim integration module imported successfully")
except ImportError as e:
    print(f"✗ Failed to import 5sim integration: {str(e)}")
    sys.exit(1)

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def test_config_loading():
    """Test configuration loading and validation"""
    print("\n=== Testing Configuration Loading ===")
    
    config = load_fivesim_config()
    print(f"✓ Configuration loaded: {len(config)} settings")
    
    # Check if enabled
    if config.get('enabled', False):
        print("✓ 5sim integration is enabled")
        if config.get('api_key', ''):
            print("✓ API key is configured")
        else:
            print("✗ API key is not configured")
    else:
        print("ℹ 5sim integration is disabled")
    
    return config

def test_5sim_manager():
    """Test 5sim manager initialization"""
    print("\n=== Testing 5sim Manager ===")
    
    manager = FiveSimManager(logger)
    
    if manager.is_available():
        print("✓ 5sim manager is available and configured")
        return manager
    else:
        print("ℹ 5sim manager is not available (disabled or not configured)")
        return None

def test_api_connection(manager):
    """Test API connection and balance check"""
    if not manager:
        print("ℹ Skipping API connection test (manager not available)")
        return False
    
    print("\n=== Testing API Connection ===")
    
    try:
        if manager.client:
            balance = manager.client.get_balance()
            print(f"✓ API connection successful, balance: ${balance}")
            
            # Check minimum balance
            min_balance = manager.config.get('min_balance_threshold', 1.0)
            if balance >= min_balance:
                print(f"✓ Balance sufficient (minimum: ${min_balance})")
                return True
            else:
                print(f"⚠ Balance insufficient (minimum: ${min_balance})")
                return False
        else:
            print("✗ Client not initialized")
            return False
    except Exception as e:
        print(f"✗ API connection failed: {str(e)}")
        return False

def test_phone_number_availability(manager):
    """Test phone number availability"""
    if not manager:
        print("ℹ Skipping phone number availability test (manager not available)")
        return
    
    print("\n=== Testing Phone Number Availability ===")
    
    try:
        if manager.client:
            availability = manager.client.get_available_numbers()
            if availability:
                qty = availability.get('Qty', 0)
                price = availability.get('Price', 0)
                print(f"✓ Available numbers: {qty} at ${price} each")
            else:
                print("⚠ No numbers available for Google verification")
    except Exception as e:
        print(f"✗ Failed to check availability: {str(e)}")

def create_sample_config():
    """Create a sample configuration file"""
    print("\n=== Creating Sample Configuration ===")
    
    sample_config = {
        "enabled": False,
        "api_key": "YOUR_5SIM_API_KEY_HERE",
        "default_country": "any",
        "default_operator": "any",
        "product": "google",
        "max_wait_time": 300,
        "check_interval": 10,
        "preferred_countries": [
            "us",
            "gb", 
            "ca",
            "fr",
            "de"
        ],
        "preferred_operators": [
            "any"
        ],
        "fallback_to_manual": True,
        "auto_finish_orders": True,
        "min_balance_threshold": 1.0,
        "retry_attempts": 3,
        "retry_delay": 30,
        "logging": {
            "level": "INFO",
            "log_api_calls": True,
            "log_sms_content": False
        },
        "error_handling": {
            "auto_cancel_on_timeout": True,
            "auto_retry_on_failure": True,
            "max_retry_attempts": 2
        }
    }
    
    config_path = "grps/PyFiles/json/fivesim_config.json"
    
    try:
        # Check if config already exists
        if os.path.exists(config_path):
            print(f"ℹ Configuration file already exists: {config_path}")
        else:
            if save_fivesim_config(sample_config, config_path):
                print(f"✓ Sample configuration created: {config_path}")
                print("ℹ Please edit the configuration file and add your 5sim API key")
            else:
                print(f"✗ Failed to create configuration file")
    except Exception as e:
        print(f"✗ Error creating sample config: {str(e)}")

def print_setup_instructions():
    """Print setup instructions for users"""
    print("\n" + "="*60)
    print("5SIM INTEGRATION SETUP INSTRUCTIONS")
    print("="*60)
    print()
    print("1. Sign up for a 5sim account at: https://5sim.net/")
    print("2. Get your API key from the account settings")
    print("3. Add funds to your 5sim account (minimum $1-2 recommended)")
    print("4. Edit the configuration file: grps/PyFiles/json/fivesim_config.json")
    print("5. Set 'enabled': true and add your API key")
    print("6. Adjust other settings as needed (countries, timeouts, etc.)")
    print()
    print("Configuration options:")
    print("- enabled: Set to true to enable 5sim integration")
    print("- api_key: Your 5sim API key")
    print("- preferred_countries: List of preferred countries for phone numbers")
    print("- max_wait_time: Maximum time to wait for SMS (seconds)")
    print("- fallback_to_manual: Whether to fall back to manual verification")
    print()
    print("The integration will automatically:")
    print("- Detect Gmail phone verification screens")
    print("- Purchase temporary phone numbers")
    print("- Enter phone numbers into verification forms")
    print("- Wait for and enter SMS verification codes")
    print("- Handle errors and fallback to manual verification")
    print()

def main():
    """Main test function"""
    print("5SIM INTEGRATION TEST SCRIPT")
    print("="*40)
    
    # Test configuration loading
    config = test_config_loading()
    
    # Test manager initialization
    manager = test_5sim_manager()
    
    # Test API connection if available
    api_available = test_api_connection(manager)
    
    # Test phone number availability if API is working
    if api_available:
        test_phone_number_availability(manager)
    
    # Create sample config if needed
    create_sample_config()
    
    # Print setup instructions
    print_setup_instructions()
    
    print("\n" + "="*60)
    print("TEST COMPLETE")
    print("="*60)
    
    if manager and manager.is_available() and api_available:
        print("✓ 5sim integration is ready for use!")
    elif manager and manager.is_available():
        print("⚠ 5sim integration is configured but API connection failed")
        print("  Please check your API key and account balance")
    else:
        print("ℹ 5sim integration is not configured")
        print("  Follow the setup instructions above to enable it")

if __name__ == "__main__":
    main()
