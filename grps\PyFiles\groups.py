# Enhanced driver imports - primary interface
from selenium.webdriver.support import expected_conditions as EC
from selenium.common.exceptions import NoSuchElementException, TimeoutException
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.common.keys import Keys
from selenium.webdriver.common.by import By
from twocaptcha import TwoCaptcha
# Keep minimal selenium imports for compatibility with existing code that might need them
import os, random, logging, json, psutil, secrets, requests

"""
Groups Management System with Enhanced SeleniumBase Integration
Phase 2.1: Migration from selenium-wire to SeleniumBase with advanced stealth capabilities

CHROME FLAGS UPDATE:
- Removed deprecated --ignore-certificate-errors flag (unsupported in newer Chrome versions)
- Using modern alternatives: --ignore-ssl-errors, --disable-certificate-transparency
- Updated for Chrome 120+ compatibility

SECURITY ALERT HANDLING:
- Enhanced detection and handling of Google's "Critical security alert" popups
- Automatic handling of suspicious sign-in activity warnings
- Intelligent response to security review pages after clicking "Check activity"
- Supports both English and French language interfaces

LANGUAGE MANAGEMENT:
- Automatic Gmail language change to French on first login (FIRST PRIORITY after login)
- Tracks language change status in both Gmail accounts map and profile configuration
- Prevents repeated language change attempts for already configured accounts
- Supports multiple methods for language detection and change (dropdown, URL params, JavaScript)
- Uses direct Google Account settings page for reliable language changes
"""

# Enhanced SeleniumBase Driver - Primary Interface
try:
    import sys
    import os
    current_dir = os.path.dirname(os.path.abspath(__file__))
    sys.path.insert(0, current_dir)
    from updated_groups import EnhancedSeleniumBaseDriver, ProfileManager, SessionManager, BehavioralSimulator
    ENHANCED_DRIVER_AVAILABLE = True
    print("Enhanced SeleniumBase Driver and components imported successfully")
except ImportError as e:
    print(f"Enhanced driver not available: {str(e)}")
    print("Migration cannot proceed without enhanced driver components")
    ENHANCED_DRIVER_AVAILABLE = False
    # Create placeholder classes to prevent import errors
    class EnhancedSeleniumBaseDriver:
        def __init__(self, *args, **kwargs):
            raise ImportError("Enhanced driver not available")
    class ProfileManager:
        def __init__(self, *args, **kwargs):
            raise ImportError("Enhanced driver not available")
    class SessionManager:
        def __init__(self, *args, **kwargs):
            raise ImportError("Enhanced driver not available")
    class BehavioralSimulator:
        def __init__(self, *args, **kwargs):
            raise ImportError("Enhanced driver not available")

# Enhanced Proxy Manager availability
try:
    from enhanced_proxy_manager import EnhancedProxyManager
    ENHANCED_PROXY_AVAILABLE = True
except ImportError:
    ENHANCED_PROXY_AVAILABLE = False
    # Create placeholder for enhanced proxy manager
    class EnhancedProxyManager:
        def __init__(self, *args, **kwargs):
            pass

# 5sim Integration for Phone Verification
try:
    from fivesim_integration import FiveSimClient, FiveSimError, FiveSimManager, load_fivesim_config
    FIVESIM_AVAILABLE = True
    print("5sim integration module imported successfully")
except ImportError as e:
    print(f"5sim integration not available: {str(e)}")
    FIVESIM_AVAILABLE = False
    # Create placeholder classes
    class FiveSimClient:
        def __init__(self, *args, **kwargs):
            pass
    class FiveSimError(Exception):
        pass
    class FiveSimManager:
        def __init__(self, *args, **kwargs):
            pass
        def is_available(self):
            return False
    def load_fivesim_config(*args, **kwargs):
        return {}

import subprocess, shutil, msvcrt, time, sys, re, os, json
from datetime import datetime
from random import choice, randint, uniform
from unidecode import unidecode
from string import digits
from time import sleep
import socket
import threading
import asyncio
from http.server import HTTPServer, BaseHTTPRequestHandler
from socketserver import ThreadingMixIn
import urllib.parse




os.environ['TF_CPP_MIN_LOG_LEVEL'] = '3'
home = os.path.dirname(os.path.realpath(__file__)).replace("\\", "/")
Files_home = home.replace('PyFiles','')
profile_home = f"{home.replace('PyFiles','')}Profiles"
data_file = f"{home.replace('PyFiles','')}data.txt"
print(data_file)
gmail_account_file = f"{home.replace('PyFiles','')}Gmail_Accounts"
data_directory = f"{home}/Data"
gmail_map_file = f"{home}/json/GmailAccountsMap.json"
map_path = f"{home}/json/GroupsMap.json"
dead_accounts = f"{home}/DeadAccounts.txt"
ua_map = f"{home}/json/ua-lib.json"
proxy_file = f"{home}/proxy.txt"
settings_path = f"{home}/json/settings.json"
cp_xpaths = [
    "//div[@id='rc-anchor-container']",
    "//div[@id='recaptcha-accessible-status']",
    "//span[contains(@class, 'recaptcha-checkbox')]"
]


class ProxyBridge:
    """
    Simple HTTP proxy bridge that forwards requests to an authenticated remote proxy.
    This allows SeleniumBase to connect to a local proxy without authentication.
    """

    def __init__(self, remote_proxy_url, local_port=8080):
        self.remote_proxy_url = remote_proxy_url
        self.local_port = local_port
        self.local_proxy_url = f"http://127.0.0.1:{local_port}"
        self.server = None
        self.server_thread = None
        self.is_running = False
        self.logger = logging.getLogger(__name__)

    def _find_free_port(self):
        """Find a free port to use for the local proxy"""
        with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as s:
            s.bind(('', 0))
            s.listen(1)
            port = s.getsockname()[1]
        return port

    def _is_port_in_use(self, port):
        """Check if a port is already in use"""
        with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as s:
            try:
                s.bind(('127.0.0.1', port))
                return False
            except OSError:
                return True

    def start(self):
        """Start the proxy bridge"""
        if self.is_running:
            self.logger.info(f"Proxy bridge already running on port {self.local_port}")
            return True

        try:
            # Import required modules for HTTP proxy
            from http.server import HTTPServer, BaseHTTPRequestHandler
            from socketserver import ThreadingMixIn
            import urllib.parse

            # Find a free port if the default is taken
            original_port = self.local_port
            while self._is_port_in_use(self.local_port):
                self.local_port = self._find_free_port()
                self.logger.info(f"Port {original_port} in use, trying {self.local_port}")

            self.local_proxy_url = f"http://127.0.0.1:{self.local_port}"

            # Create threading HTTP server class
            class ThreadingHTTPServer(ThreadingMixIn, HTTPServer):
                daemon_threads = True
                allow_reuse_address = True

            # Create proxy handler class
            class ProxyHandler(BaseHTTPRequestHandler):
                def __init__(self, remote_proxy_url, *args, **kwargs):
                    self.remote_proxy_url = remote_proxy_url
                    super().__init__(*args, **kwargs)

                def do_CONNECT(self):
                    self.send_response(200, 'Connection established')
                    self.end_headers()

                def do_GET(self):
                    self._handle_request()

                def do_POST(self):
                    self._handle_request()

                def _handle_request(self):
                    try:
                        url = self.path
                        if not url.startswith('http'):
                            url = f"http://{self.headers.get('Host', 'localhost')}{url}"

                        headers = {}
                        for name, value in self.headers.items():
                            if name.lower() not in ['connection', 'proxy-connection']:
                                headers[name] = value

                        content_length = int(self.headers.get('Content-Length', 0))
                        body = self.rfile.read(content_length) if content_length > 0 else None

                        session = requests.Session()
                        session.proxies = {'http': self.remote_proxy_url, 'https': self.remote_proxy_url}

                        response = session.request(
                            method=self.command, url=url, headers=headers, data=body, timeout=30, stream=True
                        )

                        self.send_response(response.status_code)
                        for name, value in response.headers.items():
                            if name.lower() not in ['connection', 'transfer-encoding']:
                                self.send_header(name, value)
                        self.end_headers()

                        for chunk in response.iter_content(chunk_size=8192):
                            if chunk:
                                self.wfile.write(chunk)

                    except Exception as e:
                        try:
                            self.send_error(500, f"Proxy error: {str(e)}")
                        except:
                            pass

                def log_message(self, format, *args):
                    pass  # Suppress default logging

            # Create handler factory
            def handler_factory(*args, **kwargs):
                return ProxyHandler(self.remote_proxy_url, *args, **kwargs)

            # Create and start the server
            self.server = ThreadingHTTPServer(('127.0.0.1', self.local_port), handler_factory)
            self.server_thread = threading.Thread(target=self.server.serve_forever, daemon=True)
            self.server_thread.start()

            time.sleep(1)  # Wait for server to start
            self.is_running = True
            self.logger.info(f"Proxy bridge started successfully on {self.local_proxy_url}")
            return True

        except Exception as e:
            self.logger.error(f"Failed to start proxy bridge: {str(e)}")
            return False

    def stop(self):
        """Stop the proxy bridge"""
        if self.server and self.is_running:
            try:
                self.server.shutdown()
                self.server.server_close()
                if self.server_thread:
                    self.server_thread.join(timeout=5)
                self.is_running = False
                self.logger.info("Proxy bridge stopped")
            except Exception as e:
                self.logger.error(f"Error stopping proxy bridge: {str(e)}")

    def get_local_proxy_url(self):
        """Get the local proxy URL for use with browsers"""
        if self.is_running:
            return self.local_proxy_url
        return None

    def __del__(self):
        """Cleanup when object is destroyed"""
        self.stop()


class CityName():
    def __init__(self) -> None:
        self.country_codes = ["US","AU","GB","FR","DE","IS","NO"]
        self.cd = secrets.choice(self.country_codes)
        
    def v2(self):
        url = "https://wft-geo-db.p.rapidapi.com/v1/geo/cities"
        querystring = {"limit":"10",
                        #"minPopulation": f'{pop}',
                        "countryIds": f'{self.cd}'}
        headers = {
            'x-rapidapi-host': "wft-geo-db.p.rapidapi.com",
            'x-rapidapi-key': "**************************************************"
            }
        resp = requests.request("GET", url, headers=headers, params=querystring).text
        info = json.loads(resp)
        return info
    
    def v1(self):
            url = 'https://countriesnow.space/api/v0.1/countries/population/cities'
            resp = requests.get(url).text
            info = json.loads(resp)
            return info
        
    
    
    def name(self):
        accents = ['à' ,'â', 'ä','é','è','ê' ,'ë' ,'ú' ,'ï'  ,'î' ,'ô' , 'ó'  ,'ö'  ,'ù'  ,'û' ,'ü' ,'ÿ' ,'ç','í','á', 'ạ' ,'à', 'ả', 'ã'
                'ă', 'ắ' ,'ặ', 'ằ' ,'ẳ','ẵ',
                'â', 'ấ' ,'ậ' ,'ầ', 'ẩ ','ẫ',
                'é', 'ẹ', 'è', 'ẻ', 'ẽ',
                'ê' ,'ế' ,'ệ', 'ề' ,'ể', 'ễ'
                'i' ,'í' ,'ị' ,'ì' ,'ỉ', 'ĩ']
        try:
            self.data = self.v1()
            lim = randint(1, 500)
            city = self.data['data'][lim]['city'].lower()
        except:
            self.data = self.v2()
            indx = randint(0, 9)
            city = self.data["data"][indx]["city"].lower()
        city = city.replace(" ","").replace("@","").replace(",","").replace(";","").replace("'","").replace("(","").replace(")","")
        accents_contains = any(accent in city for accent in accents)
        if accents_contains:
            city = unidecode(city)
        city = city.capitalize()
        return city



class Driver():
    """
    Fully Migrated Driver class - Uses EnhancedSeleniumBaseDriver exclusively
    Provides backward compatibility while leveraging all enhanced features
    """
    def __init__(self, email, password, ua_agent, index):
        # Setup logging
        logging.basicConfig(
            filename='app.log',
            level=logging.DEBUG,
            format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        )
        self.logger = logging.getLogger(f"EnhancedDriver-{email}")

        # Store credentials and parameters
        self.email = email
        self.password = password
        self.ua_agent = ua_agent
        self.index = index
        self.url = None

        # Ensure enhanced driver is available
        if not ENHANCED_DRIVER_AVAILABLE:
            error_msg = "Enhanced SeleniumBase Driver is required but not available. Please ensure updated_groups.py is accessible."
            self.logger.error(error_msg)
            raise ImportError(error_msg)

        try:
            self.logger.info("Initializing Enhanced SeleniumBase Driver with full feature set")

            # Create enhanced driver instance
            self._enhanced_driver = EnhancedSeleniumBaseDriver(email, password, ua_agent, index)

            # Expose browser for backward compatibility
            self.browser = self._enhanced_driver.browser

            # Log profile and session information
            if hasattr(self._enhanced_driver, 'profile_config'):
                profile_info = self._enhanced_driver.profile_config
                self.logger.info(f"Profile ID: {profile_info.get('profile_id', 'unknown')}")
                self.logger.info(f"Profile path: {profile_info.get('profile_path', 'unknown')}")

            if hasattr(self._enhanced_driver, 'session_manager') and self._enhanced_driver.session_manager:
                self.logger.info("Session management enabled")

            if hasattr(self._enhanced_driver, 'behavioral_simulator'):
                self.logger.info("Behavioral simulation enabled")

            self.logger.info("Enhanced SeleniumBase Driver initialized successfully")

        except Exception as e:
            self.logger.error(f"Failed to initialize Enhanced SeleniumBase Driver: {str(e)}")
            raise e

    # Enhanced driver handles all proxy configuration internally
    # No need for separate proxy bridge setup

    def __del__(self):
        """Cleanup when Driver object is destroyed"""
        try:
            if hasattr(self, '_enhanced_driver') and self._enhanced_driver:
                # Enhanced driver handles its own cleanup
                self._enhanced_driver.finish()
                self.logger.info("Enhanced driver cleanup completed")
        except Exception as e:
            # Use print instead of logger since logger might be destroyed
            print(f"Error during Driver cleanup: {str(e)}")

    def __getattr__(self, name):
        """
        Delegate method calls to enhanced driver for backward compatibility
        This ensures all enhanced driver methods are accessible through the Driver instance
        """
        if hasattr(self, '_enhanced_driver') and hasattr(self._enhanced_driver, name):
            return getattr(self._enhanced_driver, name)
        else:
            # Method not found in enhanced driver
            raise AttributeError(f"'{self.__class__.__name__}' object has no attribute '{name}'")

class Worker():
    def __init__(self,actions):
        super().__init__()
        self.actions = actions
        logging.basicConfig(
            filename='app.log',
            level=logging.DEBUG,
            format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        )
        self.logger = logging.getLogger("Worker")


    def terminate_selenium_driver(self):
        try:
            for process in psutil.process_iter(attrs=['pid', 'name', 'cmdline']):
                process_info = process.info
                if 'chromedriver' in process_info.get('name'):
                    process.terminate()
        except Exception as e:
            self.logger.error(f"{str(e)}")


    def remove_profile(self, email):
        try:
            profile = f"{profile_home}/{email}"
            if os.path.exists(profile):
                # Use shutil.rmtree to delete the directory and its contents
                shutil.rmtree(profile)
                self.logger.info(f"Profile {profile} removed successfully.")
            else:
                self.logger.warning(f"Profile {profile} does not exist.")
        except Exception as e:
            self.logger.error(f"Error removing profile {profile}: {str(e)}")


    def login_Wait(self):
        self.logger.warning("=== Infinite Wait Is Enable to Stop, please close browser ===")
        while self.browser.running() == True:
            sleep(0.5)

    def wait(self):
        while self.browser.running() == True:
            sleep(2.5)


    def wait_for_verification(self):
        while self.browser.running() == True and "signin/challenge/iap" in self.browser.this_url():
            sleep(2.5)



    def add_group(self, email, group):
        data = {}
        if os.path.exists(map_path):
            with open(map_path, 'r') as f:
                data = json.load(f)

        if email in data:
            for g in data[email]:
                if g["name"] == group:
                    g["members_num"] = 0
                    break
            else:
                data[email].append({
                    "name": group, 
                    "members_num": 0
                })
        else:
            data[email] = [{
                "name": group, 
                "members_num": 0
            }]

        with open(map_path, 'w') as f:
            json.dump(data, f, indent=4)



    
    def update_group_members(self, email, group, membrs_num):
        data = {}
        if os.path.exists(map_path):
            with open(map_path, 'r') as f:
                data = json.load(f)

        if email in data:
            for g in data[email]:
                if g["name"].lower() == group.lower():
                    g["members_num"] = str(membrs_num)
                    break
            else:
                self.logger.info(f"### Group {group} not found for email {email} ###")
        else:
            self.logger.info(f"### Email {email} not found ###")

        with open(map_path, 'w') as f:
            json.dump(data, f, indent=4)


    def get_groups_map(self,email):
        data = {}
        
        if os.path.exists(map_path):
            with open(map_path, 'r') as f:
                data = json.load(f)

        return data.get(email, None)
    


    def get_groups(self,email):
        if os.path.exists(map_path):
            with open(map_path, 'r') as f:
                data = json.load(f)
                groups = data.get(email, [])
                return [group['name'] for group in groups if 'name' in group]
        else:
            return None

    

    def remove_group(email, group):
        data = {}

        if os.path.exists("GroupsMap.json"):
            with open("GroupsMap.json", 'r') as f:
                data = json.load(f)

        if email in data and group in data[email]:
            data[email].remove(group)

            if not data[email]:
                del data[email]

            with open("GroupsMap.json", 'w') as f:
                json.dump(data, f)


    def update_email_status(self, email, status):
        with open(gmail_map_file, 'r') as f:
            data = json.load(f)

        for item in data:
            if item['email'] == email:
                item['status'] = status
                break

        with open(gmail_map_file, 'w') as f:
            json.dump(data, f, indent=4)


    def update_email_pass(self, email, password):
        with open(gmail_map_file, 'r') as f:
            data = json.load(f)

        for item in data:
            if item['email'] == email:
                item['password'] = password
                break

        with open(gmail_map_file, 'w') as f:
            json.dump(data, f, indent=4)


    def check_js(self,elem):
        var_js = f"return document.body.textContent.includes({elem})"
        try:
            found = self.browser.execute_js(var_js)
        except:
            found = False
        if found == "True":
            return True
        else:
            return False


    def CaptchaVerif(self):
        for xpath in cp_xpaths:
            try:
                self.browser.find_xpath(xpath)
                self.logger.error(f"Captcha Detected with XPath: {xpath}")
                return True
            except NoSuchElementException:
                continue
        return False


    def solve_captcha(self):
        api_key = "d315b270071ccc3922a75b7c56e72da1"
        solver = TwoCaptcha(api_key)
        try:
            result = solver.recaptcha(
                sitekey='6LctgAgUAAAAACsC7CsLr_jgOWQ2ul2vC_ndi8o2',
                url='https://groups.google.com/my-groups?hl=fr-FR')

        except Exception as e:
            self.logger.error(f"{str(e)}")

        else:
            #print('solved: ' + str(result))
            #print("Captcha Solved!!")
            return result['code']

   
    def CaptchaSolver(self):
        token = self.solve_captcha()

        # Try to get callback if method exists, otherwise use fallback approach
        try:
            if hasattr(self.browser, 'get_callback'):
                self.callback = self.browser.get_callback()
            else:
                # Fallback: try to find callback in JavaScript
                self.callback = self.browser.execute_js("""
                    // Try to find reCAPTCHA callback function
                    if (window.___grecaptcha_cfg && window.___grecaptcha_cfg.clients) {
                        for (let client in window.___grecaptcha_cfg.clients) {
                            let callbacks = window.___grecaptcha_cfg.clients[client].callback;
                            if (callbacks) return callbacks;
                        }
                    }
                    return null;
                """)
        except:
            self.callback = None

        try:
            cap_id = self.browser.execute_js(""" return document.querySelector('[name="g-recaptcha-response"]').id """)
        except:
            cap_id = None

        if self.callback is not None and cap_id is not None:
            try:
                self.browser.execute_js(f"""document.querySelector('#{cap_id}').innerText='{token}'; {self.callback}.call()""")
                self.logger.info("### captcha Submited!! ###")
            except:
                self.logger.error(f"+++ Can't Solve Captcha!! +++")
        else:
            # Alternative approach: try to submit the token directly
            try:
                self.browser.execute_js(f"""
                    document.querySelector('[name="g-recaptcha-response"]').innerText='{token}';
                    document.querySelector('[name="g-recaptcha-response"]').style.display = 'block';
                    // Try to trigger form submission or callback
                    if (window.grecaptcha && window.grecaptcha.getResponse) {{
                        window.grecaptcha.getResponse = function() {{ return '{token}'; }};
                    }}
                """)
                self.logger.info("### captcha token set with fallback method ###")
            except Exception as e:
                self.logger.error(f"+++ Fallback captcha method failed: {str(e)} +++")




    def passEmail(self):
        email_field = self.browser.wait_xpath_presence('//input[@type="email"]')
        try:
            # Use enhanced driver's human-like typing if available
            if hasattr(self.browser, 'human_type_text'):
                self.browser.human_type_text(email_field, self.browser.email)
            else:
                email_field.send_keys(self.browser.email)
        except:
            try:
                element = self.browser.find_xpath('#identifierId')
                if hasattr(self.browser, 'human_type_text'):
                    self.browser.human_type_text(element, self.browser.email)
                else:
                    element.send_keys(self.browser.email)
            except:
                try:
                    element = self.browser.find_css('#identifierId')
                    if hasattr(self.browser, 'human_type_text'):
                        self.browser.human_type_text(element, self.browser.email)
                    else:
                        element.send_keys(self.browser.email)
                except:
                    try:
                        self.browser.execute_js(f'document.getElementsByName("identifier")[0].value = "{self.browser.email}"')
                    except:
                        pass

        try:
            button = self.browser.find_css('#identifierNext > div > button')
            if hasattr(self.browser, 'human_click_element'):
                self.browser.human_click_element(button)
            else:
                button.click()
        except:
            try:
                button = self.browser.find_xpath("//button[contains(., 'Suivant')]")
                if hasattr(self.browser, 'human_click_element'):
                    self.browser.human_click_element(button)
                else:
                    button.click()
            except:
                try:
                    button = self.browser.find_xpath('//*[@id="identifierNext"]/div/button')
                    if hasattr(self.browser, 'human_click_element'):
                        self.browser.human_click_element(button)
                    else:
                        button.click()
                except:
                    try:
                        self.browser.execute_js(""" document.querySelector('[id="identifierNext"]').click() """)
                    except:
                        self.browser.find_xpath('//input[@type="email"]').send_keys(Keys.ENTER)


    def passPassword(self):
        password_field = self.browser.wait_css_clickable("""input[type='password']""")
        try:
            # Use enhanced driver's human-like typing if available
            if hasattr(self.browser, 'human_type_text'):
                self.browser.human_type_text(password_field, self.browser.password)
            else:
                password_field.send_keys(self.browser.password)
        except:
            try:
                element = self.browser.find_css('#password > div.aCsJod.oJeWuf > div > div.Xb9hP > input')
                if hasattr(self.browser, 'human_type_text'):
                    self.browser.human_type_text(element, self.browser.password)
                else:
                    element.send_keys(self.browser.password)
            except:
                try:
                    element = self.browser.find_xpath('//input[@aria-label="Saisissez votre mot de passe"]')
                    if hasattr(self.browser, 'human_type_text'):
                        self.browser.human_type_text(element, self.browser.password)
                    else:
                        element.send_keys(self.browser.password)
                except:
                    try:
                        self.browser.execute_js(f'document.getElementsByName("Passwd")[0].value = "{self.browser.password}"')
                    except:
                        pass

        sleep(0.8)

        try:
            button = self.browser.find_xpath('//*[@id="passwordNext"]/div/button')
            if hasattr(self.browser, 'human_click_element'):
                self.browser.human_click_element(button)
            else:
                button.click()
        except:
            try:
                button = self.browser.find_css('#passwordNext > div > button')
                if hasattr(self.browser, 'human_click_element'):
                    self.browser.human_click_element(button)
                else:
                    button.click()
            except:
                try:
                    self.browser.execute_js(""" document.querySelector('[id="passwordNext"]').click() """)
                except:
                    password_field.send_keys(Keys.ENTER)



    def signchooser(self):
        try:
            self.browser.find_xpath('//*[@id="view_container"]/div/div/div[2]/div/div[1]/div/form/span/section/div/div/div/div/ul/li[1]/div').click()
        except:
            self.browser.find_css('#view_container > div > div > div.pwWryf.bxPAYd > div > div.WEQkZc > div > form > span > section > div > div > div > div > ul > li.JDAKTe.ibdqA.W7Aapd.zpCp3.SmR8 > div').click()
        sleep(1.3)
        
        self.passPassword()

    


    def webreauth(self):
        try:
            try:
                self.browser.find_css('#identifierNext > div > button > span').click()
            except:
                try:
                    self.browser.find_xpath("//button[@type='button' and  contains(., 'Suivant') ]").click()
                except:
                    try:
                        self.browser.find_xpath('//*[@id="identifierNext"]/div/button').click()
                    except:
                        raise RuntimeError("Can't Find Next Button!!")
            
            sleep(3)
            
            self.passPassword()
        
        except Exception as e:
            self.logger.error(f"{str(e)}")
            self.terminate_selenium_driver()


    def rejected(self):
        try:
            try:
                self.browser.find_xpath('//*[@id="accountRecoveryButton"]').click()
            except:
                try:
                    self.browser.find_css('#accountRecoveryButton').click()
                except:
                    try:
                        self.browser.execute_js(""" document.querySelector('[aria-label="Continuer"]').click() """)
                    except:
                        raise RuntimeError("Can't Find Recovery Button!!")
        
            sleep(2.5)
            
            try:
                self.browser.find_xpath('//*[@id="identifierNext"]/div/button').click()
            except:
                try:
                    self.browser.find_css('#identifierNext > div > button').click()
                except:
                    try:
                        self.browser.execute_js(""" document.querySelector('[type="button"]').click() """)
                    except:
                        raise RuntimeError("Can't Find Next Button!!")
                        
            sleep(2.2)

            self.passPassword()
            
            sleep(2.5)

            try:
                self.browser.find_xpath('//*[@id="knowledgePreregisteredEmailInput"]').send_keys(self.conf)
            except:
                try:
                    self.browser.find_css('#knowledgePreregisteredEmailInput').send_keys(self.conf)
                except:
                    try:
                        self.browser.execute_js(f""" document.querySelector('[type="email"]').value = "{self.conf}" """)
                    except:
                        raise RuntimeError("Can't Find Email Input!!")
        except Exception as e:
            self.logger.error(f"{str(e)}")
            self.terminate_selenium_driver()


    def phoneVerif(self):
        self.logger.info(f"Checking Phone Recovery!!")
        phone_recov = False

        try:
            # Enhanced phone recovery detection with multiple patterns
            phone_recovery_patterns = [
                "//*[contains(text(),'Obtenir un code de validation')]",  # French
                "//*[contains(text(),'Get a verification code')]",        # English
                "//*[contains(text(),'Confirmez qu')]",                    # French confirmation
                "//*[contains(text(),'Verify it')]",                      # English verification
                "//*[contains(text(),'numéro de téléphone')]",            # French phone number
                "//*[contains(text(),'phone number')]",                   # English phone number
                "//*[contains(text(),'SMS')]",                            # SMS text
                "//*[contains(text(),'code de validation')]",             # French validation code
                "//*[contains(text(),'validation code')]",                # English validation code
                "//input[@type='tel']",                                   # Phone input field
                "//input[@name='phoneNumber']",                           # Phone number input
                "//button[contains(text(),'Suivant')]",                   # French Next button
                "//button[contains(text(),'Next')]"                       # English Next button
            ]

            for pattern in phone_recovery_patterns:
                try:
                    element = self.browser.find_xpath(pattern)
                    if element:
                        self.logger.info(f"Phone Recovery Detected via pattern: {pattern}")
                        self.logger.info(f"Element found: {element}")
                        phone_recov = True
                        break
                except:
                    continue

        except Exception as e:
            self.logger.error(f"Error in phone recovery detection: {str(e)}")

        # Also check URL patterns
        current_url = self.browser.this_url()
        url_patterns = [
            "signin/challenge/iap",
            "signin/challenge/ipp",
            "signin/v2/challenge/selection",
            "accounts.google.com/signin/v2/challenge"
        ]

        url_detected = any(pattern in current_url for pattern in url_patterns)
        if url_detected:
            self.logger.info(f"Phone Recovery Detected via URL: {current_url}")
            phone_recov = True

        # Check page content with JavaScript
        try:
            page_content_indicators = [
                "Confirmez qu'il s'agit bien de vous",
                "Verify it's you",
                "Saisissez un numéro de téléphone",
                "Enter a phone number",
                "code de validation",
                "verification code"
            ]

            for indicator in page_content_indicators:
                if self.check_js(f'"{indicator}"'):
                    self.logger.info(f"Phone Recovery Detected via page content: {indicator}")
                    phone_recov = True
                    break

        except Exception as e:
            self.logger.error(f"Error checking page content: {str(e)}")

        if phone_recov:
            self.logger.warning(f"### PHONE RECOVERY REQUIRED ###")
            self.update_email_status(self.browser.email, "phone_verification_required")

            # Try to handle phone verification automatically
            if hasattr(self, '_handle_phone_verification'):
                self.logger.info("Attempting automatic phone verification handling...")
                self._handle_phone_verification()
            else:
                self.logger.info("Waiting for manual phone verification...")
                self.wait_for_verification()
        else:
            # Try to click "Don't ask again on this device" if available
            try:
                dont_ask_patterns = [
                    "//*[contains(text(),'Ne plus me demander sur cet appareil')]",  # French
                    "//*[contains(text(),'Don\\'t ask again on this device')]",      # English
                    "//*[contains(text(),'Remember this device')]",                 # Alternative English
                    "//*[contains(text(),'Se souvenir de cet appareil')]"           # Alternative French
                ]

                for pattern in dont_ask_patterns:
                    try:
                        element = self.browser.find_xpath(pattern)
                        if element:
                            if hasattr(self.browser, 'human_click_element'):
                                self.browser.human_click_element(element)
                            else:
                                element.click()
                            self.logger.info(f"Clicked 'Don't ask again' button: {pattern}")
                            sleep(uniform(1.0, 2.0))
                            break
                    except:
                        continue

            except Exception as e:
                self.logger.error(f"Error clicking 'Don't ask again': {str(e)}")

        self.logger.info(f"Phone Recovery >> {phone_recov}")
        return phone_recov



    def change_password(self):
        self.new_pass = f"@{self.browser.password}@"

        try:
            self.browser.wait_xpath_presence('//*[@id="passwd"]/div[1]/div/div[1]/input')
        except:
            self.browser.wait_xpath_presence('//*[@id="Password"]')
        try:
            self.browser.find_xpath('//*[@id="passwd"]/div[1]/div/div[1]/input').send_keys(self.new_pass)
        except:
            self.browser.find_xpath('//*[@id="Password"]').send_keys(self.new_pass)
        sleep(0.5)
        try:
            self.browser.find_xpath('//*[@id="confirm-passwd"]/div[1]/div/div[1]/input').send_keys(self.new_pass)
            self.browser.find_xpath('//*[@id="confirm-passwd"]/div[1]/div/div[1]/input').send_keys(Keys.ENTER)
        except:
            self.browser.find_xpath('//*[@id="ConfirmPassword"]').send_keys(self.new_pass)
            self.browser.find_xpath('//*[@id="ConfirmPassword"]').send_keys(Keys.ENTER)
        
        self.update_email_pass(self.browser.email, self.new_pass)
        self.logger.info(f"### Password Changed!! ###")


    def login(self):
        #### &hl=fr-FR ####
        #sleep(3)

        self.passEmail()
        
        sleep(2)

        if self.CaptchaVerif():
            self.CaptchaSolver()

        sleep(2)

        self.passPassword()

        sleep(3)

        try:
            if self.CaptchaVerif():
                self.CaptchaSolver()
        except:
            pass
        
        sleep(1)

        try:
            toggle_button = self.browser.wait_css_clickable("#confirm")
            toggle_button.click()
        except TimeoutException:
            self.logger.info("No 'I understand' button found")

        self.phoneVerif()

        sleep(1.5)

        if"speedbump/changepassword" in self.browser.this_url():
            self.change_password()

        # FIRST PRIORITY: Check if we need to change the account language to French
        # This should be done immediately after successful login, before other actions
        try:
            if self._should_change_language_to_french():
                self.logger.info("First login detected - changing account language to French")
                self._change_gmail_language_to_french()
        except Exception as e:
            self.logger.error(f"Error changing language to French: {str(e)}")

        # SECOND PRIORITY: Check for critical security alert popup after language change
        # This handles any security popups that may appear after account modifications
        try:
            if self._detect_suspicious_activity():
                self.logger.info("Critical security alert detected after login/language change")
                self._handle_suspicious_activity()
        except Exception as e:
            self.logger.error(f"Error handling security alert during login: {str(e)}")

    

    def export_ck_lc(self):
        self.browser.go("https://google.com")
        cookies = self.browser.get_cookies()
        cookies_path = os.path.join(home, "Cookies", self.browser.email)

        if not os.path.exists(cookies_path):
            os.makedirs(cookies_path)

        with open(os.path.join(cookies_path, 'cookies.json'), 'w') as f:
            json.dump(cookies, f, indent=4)

        local_storage = self.browser.execute_js(
            "let ls = {}; for (let i = 0; i < localStorage.length; i++) { "
            "let key = localStorage.key(i); ls[key] = localStorage.getItem(key); } return ls;"
        )

        with open(os.path.join(cookies_path, 'localStorage.json'), 'w') as f:
            json.dump(local_storage, f, indent=4)

        self.logger.info("### Cookies and Local Storage Exported!! ###")
        return


    def perform_human_actions_after_login(self):
        """
        Perform realistic human actions after successful Gmail login to warm up the account
        and establish natural browsing patterns. This helps avoid detection and makes the
        account appear more legitimate.
        """
        self.logger.info("### Starting Human Actions After Login ###")

        try:
            # FIRST PRIORITY: Check if we need to change the account language to French
            # This should be done before any other human actions
            try:
                if self._should_change_language_to_french():
                    self.logger.info("First login detected during human actions - changing account language to French")
                    self._change_gmail_language_to_french()
            except Exception as e:
                self.logger.error(f"Error changing language to French during human actions: {str(e)}")

            # SECOND PRIORITY: Check for phone verification or other security challenges
            if self._handle_post_login_security_challenges():
                self.logger.info("Security challenges detected and handled")
                return  # Exit human actions if phone verification is required

            # Phase 0: Enable Chrome Sync automatically
            self._enable_chrome_sync_automatically()

            # Check for sync popup throughout the process
            self._handle_chrome_sync_popup()

            # Handle cookie warning dialog
            self._handle_cookie_warning()

            # Phase 1: Initial account exploration (2-4 minutes)
            self._explore_google_services()

            # Check for sync popup again
            self._handle_chrome_sync_popup()

            # Check for cookie warning again
            self._handle_cookie_warning()

            # Phase 2: Gmail interaction (3-5 minutes)
            self._interact_with_gmail()
            self._handle_chrome_sync_popup()  # Check again

            # Phase 3: Google Search activity (2-3 minutes)
            self._perform_search_activities()
            self._handle_chrome_sync_popup()  # Check again

            # Phase 4: Account settings check (1-2 minutes)
            self._check_account_settings()
            self._handle_chrome_sync_popup()  # Check again

            # Phase 5: Return to main Google page
            self._return_to_google_home()
            self._handle_chrome_sync_popup()  # Final check

            self.logger.info("### Human Actions After Login Completed Successfully ###")

        except Exception as e:
            self.logger.error(f"Error during human actions: {str(e)}")
            # Continue with the main flow even if human actions fail
            pass

    def _enable_chrome_sync_automatically(self):
        """Enable Chrome sync automatically after login"""
        self.logger.info("Phase 0: Enabling Chrome Sync automatically...")

        try:
            # First, check if Chrome sync popup is already visible on current page
            self._handle_chrome_sync_popup()

            # Navigate to Chrome sync settings
            self.browser.go("chrome://settings/syncSetup")
            sleep(uniform(2.0, 3.0))

            # Check for sync popup again after navigation
            self._handle_chrome_sync_popup()

            # Try to click "Turn on sync" button if it exists
            try:
                sync_button = self.browser.find_xpath("//button[contains(text(), 'Turn on sync') or contains(text(), 'Activer la synchronisation')]")
                if sync_button and sync_button.is_displayed():
                    if hasattr(self.browser, 'human_click_element'):
                        self.browser.human_click_element(sync_button)
                    else:
                        sync_button.click()
                    sleep(uniform(2.0, 3.0))
                    self.logger.info("Chrome sync enabled successfully")
                else:
                    self.logger.info("Chrome sync already enabled or button not found")
            except:
                # Try alternative selectors
                try:
                    sync_button = self.browser.find_css("[data-sync-promo-action='turn-on-sync']")
                    if sync_button and sync_button.is_displayed():
                        if hasattr(self.browser, 'human_click_element'):
                            self.browser.human_click_element(sync_button)
                        else:
                            sync_button.click()
                        sleep(uniform(2.0, 3.0))
                        self.logger.info("Chrome sync enabled successfully (alternative method)")
                except:
                    self.logger.info("Chrome sync button not found - may already be enabled")

            # Navigate back to Google
            self.browser.go("https://www.google.com")
            sleep(uniform(1.0, 2.0))

            # Final check for sync popup
            self._handle_chrome_sync_popup()

        except Exception as e:
            self.logger.error(f"Error enabling Chrome sync: {str(e)}")
            # Continue anyway - sync may already be enabled
            pass

    def _handle_chrome_sync_popup(self):
        """Handle Chrome sync popup dialog"""
        try:
            # Look for the sync popup dialog
            popup_selectors = [
                "//button[contains(text(), 'Continuer en tant que Paris')]",  # Exact text from your screenshot
                "//button[contains(text(), 'Continuer en tant que')]",  # French
                "//button[contains(text(), 'Continue as')]",  # English
                "//button[contains(text(), 'Continuer')]",  # Generic French continue
                "//button[contains(text(), 'Continue')]",  # Generic English continue
                "//div[contains(@class, 'sync-promo')]//button",  # Generic sync promo button
                "//div[contains(text(), 'Votre Chrome')]/..//button",  # Based on your screenshot
                "//div[contains(text(), 'Pour accéder à vos mots de passe')]/..//button",  # From screenshot text
            ]

            for selector in popup_selectors:
                try:
                    popup_button = self.browser.find_xpath(selector)
                    if popup_button and popup_button.is_displayed():
                        self.logger.info(f"Found Chrome sync popup, clicking continue button")
                        if hasattr(self.browser, 'human_click_element'):
                            self.browser.human_click_element(popup_button)
                        else:
                            popup_button.click()
                        sleep(uniform(2.0, 3.0))
                        self.logger.info("Chrome sync popup handled successfully")
                        return True
                except:
                    continue

            # Also try CSS selectors
            css_selectors = [
                "[data-sync-promo-action]",
                ".sync-promo button",
                ".signin-promo button"
            ]

            for selector in css_selectors:
                try:
                    popup_button = self.browser.find_css(selector)
                    if popup_button and popup_button.is_displayed():
                        self.logger.info(f"Found Chrome sync popup (CSS), clicking continue button")
                        if hasattr(self.browser, 'human_click_element'):
                            self.browser.human_click_element(popup_button)
                        else:
                            popup_button.click()
                        sleep(uniform(2.0, 3.0))
                        self.logger.info("Chrome sync popup handled successfully (CSS)")
                        return True
                except:
                    continue

            return False

        except Exception as e:
            self.logger.error(f"Error handling Chrome sync popup: {str(e)}")
            return False

    def _handle_cookie_warning(self):
        """Handle cookie warning dialog"""
        try:
            # Look for cookie warning dialog elements
            cookie_warning_selectors = [
                "//div[contains(text(), 'problème dans votre paramétrage des cookies')]",  # French from your screenshot
                "//div[contains(text(), 'cookie problem')]",  # English equivalent
                "//div[contains(text(), 'Nous avons détecté un problème')]",  # From your screenshot
                "//div[contains(text(), 'Activer les cookies')]",  # "Activate cookies" from screenshot
            ]

            for selector in cookie_warning_selectors:
                try:
                    warning_element = self.browser.find_xpath(selector)
                    if warning_element and warning_element.is_displayed():
                        self.logger.info("Cookie warning detected, attempting to resolve...")

                        # Try to find and click links to resolve the issue
                        resolution_links = [
                            "//a[contains(text(), 'instructions propres à chaque navigateur')]",  # From screenshot
                            "//a[contains(text(), 'videz le cache')]",  # Clear cache link
                            "//a[contains(text(), 'supprimez les cookies')]",  # Delete cookies link
                            "//a[contains(@href, 'support.google.com')]",  # Google support links
                        ]

                        for link_selector in resolution_links:
                            try:
                                link = self.browser.find_xpath(link_selector)
                                if link and link.is_displayed():
                                    self.logger.info("Found resolution link, clicking...")
                                    if hasattr(self.browser, 'human_click_element'):
                                        self.browser.human_click_element(link)
                                    else:
                                        link.click()
                                    sleep(uniform(2.0, 3.0))
                                    return True
                            except:
                                continue

                        # If no resolution links found, try to navigate away and back
                        self.logger.info("No resolution links found, refreshing page...")
                        self.browser.refresh()
                        sleep(uniform(2.0, 3.0))
                        return True

                except:
                    continue

            return False

        except Exception as e:
            self.logger.error(f"Error handling cookie warning: {str(e)}")
            return False

    def _handle_post_login_security_challenges(self):
        """
        Handle various security challenges that may appear after login
        Returns True if a security challenge was detected (human actions should stop)
        Returns False if no challenges detected (continue with human actions)
        """
        self.logger.info("Checking for post-login security challenges...")

        try:
            # Check for phone verification challenge
            if self._detect_phone_verification():
                return self._handle_phone_verification()

            # Check for 2FA/MFA challenges
            if self._detect_two_factor_auth():
                return self._handle_two_factor_auth()

            # Check for suspicious activity warnings
            if self._detect_suspicious_activity():
                return self._handle_suspicious_activity()

            # Check for account recovery challenges
            if self._detect_account_recovery():
                return self._handle_account_recovery()

            # Check for terms of service updates
            if self._detect_terms_update():
                return self._handle_terms_update()

            self.logger.info("No security challenges detected, proceeding with human actions")
            return False

        except Exception as e:
            self.logger.error(f"Error checking security challenges: {str(e)}")
            return False

    def _detect_phone_verification(self):
        """Detect phone number verification screen with 5sim integration support"""
        try:
            # Check URL patterns
            current_url = self.browser.this_url()
            phone_url_patterns = [
                "signin/challenge/iap",
                "signin/challenge/ipp",
                "signin/v2/challenge/selection",
                "accounts.google.com/signin/v2/challenge"
            ]

            if any(pattern in current_url for pattern in phone_url_patterns):
                self.logger.info(f"Phone verification detected via URL: {current_url}")
                return True

            # Check for French text patterns (as shown in screenshot)
            french_phone_indicators = [
                "Confirmez qu'il s'agit bien de vous",
                "Saisissez un numéro de téléphone",
                "numéro de téléphone pour recevoir un SMS",
                "code de validation",
                "Essayer une autre méthode"
            ]

            # Check for English text patterns
            english_phone_indicators = [
                "Verify it's you",
                "Enter a phone number",
                "phone number to get a text",
                "verification code",
                "Try another way",
                "2-Step Verification"
            ]

            all_indicators = french_phone_indicators + english_phone_indicators

            for indicator in all_indicators:
                if self.check_js(f'"{indicator}"'):
                    self.logger.info(f"Phone verification detected via text: {indicator}")
                    return True

            # Check for specific elements - prioritize phoneNumberId for 5sim integration
            phone_elements = [
                '//input[@id="phoneNumberId"]',  # Primary target for 5sim integration
                '//input[@type="tel"]',  # Phone input field
                '//input[@name="phoneNumber"]',
                '//div[contains(text(), "phone")]',
                '//button[contains(text(), "Suivant")]',  # French "Next" button
                '//button[contains(text(), "Next")]',
                '//a[contains(text(), "Try another way")]',
                '//a[contains(text(), "Essayer une autre méthode")]'  # French "Try another way"
            ]

            for xpath in phone_elements:
                try:
                    element = self.browser.find_xpath(xpath)
                    if element:
                        self.logger.info(f"Phone verification detected via element: {xpath}")

                        # Special handling for phoneNumberId - mark for 5sim integration
                        if xpath == '//input[@id="phoneNumberId"]':
                            self.logger.info("Detected phoneNumberId field - 5sim integration will be triggered")
                            # Store this information for the handler
                            self._phone_verification_type = "phoneNumberId"

                        return True
                except:
                    continue

            return False

        except Exception as e:
            self.logger.error(f"Error detecting phone verification: {str(e)}")
            return False

    def _handle_phone_verification(self):
        """Handle phone verification challenge with 5sim integration"""
        self.logger.warning("### PHONE VERIFICATION REQUIRED ###")
        self.logger.warning("Google is requesting phone number verification")

        try:
            # Update account status to indicate phone verification needed
            self.update_email_status(self.browser.email, "phone_verification_required")

            # Check if we have a phone number in the account data first
            phone_number = self._get_account_phone_number()

            if phone_number:
                self.logger.info(f"Found phone number for account: {phone_number}")
                return self._attempt_phone_verification(phone_number)

            # No phone number available - try 5sim integration if available
            if FIVESIM_AVAILABLE:
                self.logger.info("No phone number in account data - attempting 5sim integration")
                return self._handle_phone_verification_with_5sim()
            else:
                self.logger.warning("5sim integration not available")
                return self._handle_phone_verification_without_number()

        except Exception as e:
            self.logger.error(f"Error handling phone verification: {str(e)}")
            return True

    def _get_account_phone_number(self):
        """Get phone number from account data"""
        try:
            with open(gmail_map_file, 'r') as f:
                data = json.load(f)

            for account in data:
                if account.get('email') == self.browser.email:
                    phone = account.get('phone', '')
                    if phone and not '@' in phone:  # Make sure it's a phone, not email
                        return phone

            return None

        except Exception as e:
            self.logger.error(f"Error getting phone number: {str(e)}")
            return None

    def _handle_phone_verification_with_5sim(self):
        """Handle phone verification using 5sim service"""
        try:
            # Initialize 5sim manager
            fivesim_manager = FiveSimManager(self.logger)

            if not fivesim_manager.is_available():
                self.logger.warning("5sim integration not available or not configured")
                return self._handle_phone_verification_without_number()

            # Get phone number from 5sim
            self.logger.info("Requesting phone number from 5sim...")
            phone_number = fivesim_manager.get_phone_number_for_gmail()

            if not phone_number:
                self.logger.error("Failed to get phone number from 5sim")
                return self._handle_phone_verification_without_number()

            # Attempt to enter the phone number
            self.logger.info(f"Entering phone number: {phone_number}")
            success = self._attempt_phone_verification(phone_number)

            if not success:
                self.logger.error("Failed to enter phone number")
                fivesim_manager.cancel_current_order()
                return self._handle_phone_verification_without_number()

            # Wait for SMS verification code and enter it automatically
            self.logger.info("Waiting for SMS verification code...")
            code_success = self._wait_and_enter_sms_code_with_5sim(fivesim_manager)

            if code_success:
                # Mark order as finished
                fivesim_manager.finish_current_order()
                self.logger.info("Phone verification completed successfully with 5sim")
                return True
            else:
                self.logger.error("Failed to enter SMS verification code")
                fivesim_manager.cancel_current_order()
                return self._handle_phone_verification_without_number()

        except Exception as e:
            self.logger.error(f"Error in 5sim phone verification: {str(e)}")
            return self._handle_phone_verification_without_number()

    def _wait_and_enter_sms_code_with_5sim(self, fivesim_manager):
        """Wait for SMS input field and automatically retrieve and enter code from 5sim"""
        try:
            self.logger.info("Waiting for SMS code input field to appear...")

            # Wait for SMS code input field to appear (up to 60 seconds)
            max_wait_for_field = 60
            wait_time = 0
            sms_input = None

            sms_selectors = [
                '//input[@type="text" and contains(@placeholder, "code")]',
                '//input[@name="smsUserPin"]',
                '//input[@id="smsUserPin"]',
                '//input[contains(@placeholder, "verification")]',
                '//input[contains(@placeholder, "validation")]',
                '//input[contains(@placeholder, "vérification")]',  # French
                '//input[@type="text" and @maxlength="6"]',  # Common for 6-digit codes
                '//input[@type="text" and @maxlength="8"]',  # Common for 8-digit codes
                '//input[@type="tel" and @maxlength="6"]',   # Tel input for codes
                '//input[@type="tel" and @maxlength="8"]'    # Tel input for codes
            ]

            while wait_time < max_wait_for_field and not sms_input:
                for selector in sms_selectors:
                    try:
                        sms_input = self.browser.find_xpath(selector)
                        if sms_input:
                            self.logger.info(f"Found SMS code input field: {selector}")
                            break
                    except:
                        continue

                if not sms_input:
                    sleep(2)
                    wait_time += 2

            if not sms_input:
                self.logger.error("SMS code input field not found after 60 seconds")
                fivesim_manager.cancel_current_order()
                return False

            # Now wait for SMS code from 5sim and enter it
            self.logger.info("SMS input field found, now waiting for SMS code from 5sim...")

            # Wait for SMS code with timeout
            verification_code = fivesim_manager.wait_for_verification_code(timeout=300)  # 5 minutes

            if not verification_code:
                self.logger.error("SMS verification code not received from 5sim")
                fivesim_manager.cancel_current_order()
                return False

            # Enter the verification code
            self.logger.info(f"Received SMS code: {verification_code}")
            self.logger.info("Entering verification code into input field...")

            # Clear and enter verification code
            sms_input.clear()
            sleep(uniform(0.5, 1.0))

            if hasattr(self.browser, 'human_type_text'):
                self.browser.human_type_text(sms_input, verification_code, clear_first=False)
            else:
                sms_input.send_keys(verification_code)

            sleep(uniform(1.0, 2.0))

            # Try to submit the code
            submit_success = self._submit_sms_verification_code(sms_input)

            if submit_success:
                # Mark order as finished
                fivesim_manager.finish_current_order()
                self.logger.info("SMS verification completed successfully with 5sim")
                return True
            else:
                self.logger.error("Failed to submit SMS verification code")
                fivesim_manager.cancel_current_order()
                return False

        except Exception as e:
            self.logger.error(f"Error in SMS code handling with 5sim: {str(e)}")
            fivesim_manager.cancel_current_order()
            return False

    def _submit_sms_verification_code(self, sms_input):
        """Submit the SMS verification code"""
        try:
            # Try to click Next/Continue/Verify button
            submit_buttons = [
                '//button[contains(text(), "Next")]',
                '//button[contains(text(), "Continue")]',
                '//button[contains(text(), "Verify")]',
                '//button[contains(text(), "Submit")]',
                '//button[contains(text(), "Suivant")]',  # French
                '//button[contains(text(), "Continuer")]',  # French
                '//button[contains(text(), "Vérifier")]',  # French Verify
                '//input[@type="submit"]',
                '//button[@type="submit"]',
                '//*[@id="next"]',
                '//div[@role="button" and contains(text(), "Next")]',
                '//div[@role="button" and contains(text(), "Suivant")]'
            ]

            for button_xpath in submit_buttons:
                try:
                    button = self.browser.find_xpath(button_xpath)
                    if button:
                        self.logger.info(f"Found submit button: {button_xpath}")
                        if hasattr(self.browser, 'human_click_element'):
                            self.browser.human_click_element(button)
                        else:
                            button.click()

                        self.logger.info("SMS verification code submitted successfully")
                        sleep(uniform(2.0, 4.0))
                        return True
                except Exception as e:
                    self.logger.debug(f"Submit button failed: {button_xpath} - {str(e)}")
                    continue

            # If no button found, try pressing Enter
            try:
                self.logger.info("No submit button found, trying Enter key...")
                sms_input.send_keys(Keys.RETURN)
                self.logger.info("SMS verification code submitted via Enter key")
                sleep(uniform(2.0, 4.0))
                return True
            except Exception as e:
                self.logger.warning(f"Enter key submission failed: {str(e)}")

            self.logger.warning("Could not find submit button or use Enter key for SMS verification")
            return False

        except Exception as e:
            self.logger.error(f"Error submitting SMS verification code: {str(e)}")
            return False

    def _enter_sms_verification_code(self, verification_code):
        """Enter SMS verification code into the input field"""
        try:
            self.logger.info(f"Entering SMS verification code: {verification_code}")

            # Wait for SMS code input field to appear
            max_wait = 30
            wait_time = 0
            sms_input = None

            sms_selectors = [
                '//input[@type="text" and contains(@placeholder, "code")]',
                '//input[@name="smsUserPin"]',
                '//input[@id="smsUserPin"]',
                '//input[contains(@placeholder, "verification")]',
                '//input[contains(@placeholder, "validation")]',
                '//input[@type="text" and @maxlength="6"]',  # Common for 6-digit codes
                '//input[@type="text" and @maxlength="8"]'   # Common for 8-digit codes
            ]

            while wait_time < max_wait and not sms_input:
                for selector in sms_selectors:
                    try:
                        sms_input = self.browser.find_xpath(selector)
                        if sms_input:
                            self.logger.info(f"Found SMS code input field: {selector}")
                            break
                    except:
                        continue

                if not sms_input:
                    sleep(1)
                    wait_time += 1

            if sms_input:
                # Clear and enter verification code
                sms_input.clear()
                sleep(uniform(0.5, 1.0))

                if hasattr(self.browser, 'human_type_text'):
                    self.browser.human_type_text(sms_input, verification_code, clear_first=False)
                else:
                    sms_input.send_keys(verification_code)

                sleep(uniform(1.0, 2.0))

                # Try to click Next/Continue/Verify button
                submit_buttons = [
                    '//button[contains(text(), "Next")]',
                    '//button[contains(text(), "Continue")]',
                    '//button[contains(text(), "Verify")]',
                    '//button[contains(text(), "Submit")]',
                    '//button[contains(text(), "Suivant")]',  # French
                    '//button[contains(text(), "Continuer")]',  # French
                    '//input[@type="submit"]',
                    '//button[@type="submit"]'
                ]

                for button_xpath in submit_buttons:
                    try:
                        button = self.browser.find_xpath(button_xpath)
                        if button:
                            if hasattr(self.browser, 'human_click_element'):
                                self.browser.human_click_element(button)
                            else:
                                button.click()

                            self.logger.info("SMS verification code submitted successfully")
                            sleep(uniform(2.0, 4.0))
                            return True
                    except:
                        continue

                # If no button found, try pressing Enter
                try:
                    sms_input.send_keys(Keys.RETURN)
                    self.logger.info("SMS verification code submitted via Enter key")
                    sleep(uniform(2.0, 4.0))
                    return True
                except:
                    pass

                self.logger.warning("Could not find submit button for SMS verification code")
                return False
            else:
                self.logger.error("SMS code input field not found")
                return False

        except Exception as e:
            self.logger.error(f"Error entering SMS verification code: {str(e)}")
            return False

    def _attempt_phone_verification(self, phone_number):
        """Attempt to enter phone number for verification"""
        try:
            self.logger.info(f"Attempting phone verification with: {phone_number}")

            # Try to find phone input field
            phone_input = None
            phone_selectors = [
                '//input[@type="tel"]',
                '//input[@name="phoneNumber"]',
                '//input[@id="phoneNumberId"]',
                '//input[contains(@placeholder, "phone")]',
                '//input[contains(@placeholder, "téléphone")]'
            ]

            for selector in phone_selectors:
                try:
                    phone_input = self.browser.find_xpath(selector)
                    if phone_input:
                        break
                except:
                    continue

            if phone_input:
                # Clear and enter phone number
                phone_input.clear()
                sleep(uniform(1.0, 2.0))

                if hasattr(self.browser, 'human_type_text'):
                    self.browser.human_type_text(phone_input, phone_number, clear_first=False)
                else:
                    phone_input.send_keys(phone_number)

                sleep(uniform(2.0, 3.0))

                # Try to click Next/Continue button
                button_clicked = False
                next_buttons = [
                    '//button[contains(text(), "Suivant")]',  # French
                    '//button[contains(text(), "Next")]',     # English
                    '//button[contains(text(), "Continue")]',
                    '//button[contains(text(), "Continuer")]',  # French Continue
                    '//button[@type="submit"]',
                    '//*[@id="next"]',
                    '//*[@id="identifierNext"]',  # Google's common Next button ID
                    '//div[@role="button" and contains(text(), "Suivant")]',  # Div buttons
                    '//span[contains(text(), "Suivant")]/parent::button',  # Button with span
                    '//span[contains(text(), "Suivant")]/ancestor::div[@role="button"]'  # Div role button
                ]

                for button_xpath in next_buttons:
                    try:
                        button = self.browser.find_xpath(button_xpath)
                        if button:
                            self.logger.info(f"Found Next button with selector: {button_xpath}")
                            if hasattr(self.browser, 'human_click_element'):
                                self.browser.human_click_element(button)
                            else:
                                button.click()

                            self.logger.info("Phone number submitted via button click, waiting for SMS code...")
                            sleep(uniform(3.0, 5.0))
                            button_clicked = True
                            break
                    except Exception as e:
                        self.logger.debug(f"Button selector failed: {button_xpath} - {str(e)}")
                        continue

                # If no button was clicked, try pressing Enter as fallback
                if not button_clicked:
                    try:
                        self.logger.info("No Next button found, trying Enter key as fallback...")
                        phone_input.send_keys(Keys.RETURN)
                        self.logger.info("Phone number submitted via Enter key, waiting for SMS code...")
                        sleep(uniform(3.0, 5.0))
                    except Exception as e:
                        self.logger.warning(f"Enter key fallback failed: {str(e)}")
                        # Continue anyway, maybe the form was submitted

                # Wait for SMS code input or next step
                self._wait_for_sms_code_input()
                return True

            else:
                self.logger.error("Could not find phone input field")
                return True

        except Exception as e:
            self.logger.error(f"Error attempting phone verification: {str(e)}")
            return True

    def _wait_for_sms_code_input(self):
        """Wait for SMS code input field to appear"""
        try:
            self.logger.info("Waiting for SMS code input field...")

            # Wait up to 30 seconds for SMS code field
            max_wait = 30
            wait_time = 0

            while wait_time < max_wait:
                # Check for SMS code input field
                sms_selectors = [
                    '//input[@type="text" and contains(@placeholder, "code")]',
                    '//input[@name="smsUserPin"]',
                    '//input[@id="smsUserPin"]',
                    '//input[contains(@placeholder, "verification")]',
                    '//input[contains(@placeholder, "validation")]'
                ]

                for selector in sms_selectors:
                    try:
                        if self.browser.find_xpath(selector):
                            self.logger.info("SMS code input field detected")
                            self.logger.warning("### MANUAL INTERVENTION REQUIRED ###")
                            self.logger.warning("Please enter the SMS verification code manually")
                            self.logger.warning("The script will wait for manual completion...")

                            # Wait for user to complete verification
                            self._wait_for_verification_completion()
                            return
                    except:
                        continue

                sleep(1)
                wait_time += 1

            self.logger.warning("SMS code input field not found within timeout")

        except Exception as e:
            self.logger.error(f"Error waiting for SMS code: {str(e)}")

    def _wait_for_verification_completion(self):
        """Wait for user to complete phone verification manually"""
        try:
            self.logger.info("Waiting for manual phone verification completion...")

            max_wait = 300  # 5 minutes
            wait_time = 0

            while wait_time < max_wait:
                current_url = self.browser.this_url()

                # Check if we're past the verification screen
                if not any(pattern in current_url for pattern in [
                    "signin/challenge/iap",
                    "signin/challenge/ipp",
                    "signin/v2/challenge"
                ]):
                    self.logger.info("Phone verification appears to be completed")
                    return

                # Check for success indicators
                success_indicators = [
                    "myaccount.google.com",
                    "accounts.google.com/ManageAccount",
                    "mail.google.com"
                ]

                if any(indicator in current_url for indicator in success_indicators):
                    self.logger.info("Successfully completed phone verification")
                    return

                sleep(2)
                wait_time += 2

            self.logger.warning("Phone verification timeout reached")

        except Exception as e:
            self.logger.error(f"Error waiting for verification completion: {str(e)}")

    def _handle_phone_verification_without_number(self):
        """Handle phone verification when no phone number is available"""
        try:
            self.logger.warning("No phone number available - looking for alternative methods")

            # Try to click "Try another way" / "Essayer une autre méthode"
            alternative_buttons = [
                '//a[contains(text(), "Try another way")]',
                '//a[contains(text(), "Essayer une autre méthode")]',
                '//button[contains(text(), "Try another way")]',
                '//button[contains(text(), "Essayer une autre méthode")]',
                '//*[contains(text(), "Skip")]',
                '//*[contains(text(), "Ignorer")]'
            ]

            for button_xpath in alternative_buttons:
                try:
                    button = self.browser.find_xpath(button_xpath)
                    if button:
                        self.logger.info(f"Clicking alternative method: {button_xpath}")
                        if hasattr(self.browser, 'human_click_element'):
                            self.browser.human_click_element(button)
                        else:
                            button.click()

                        sleep(uniform(2.0, 4.0))
                        return self._handle_alternative_verification_methods()
                except:
                    continue

            # If no alternative found, wait for manual intervention
            self.logger.warning("### MANUAL INTERVENTION REQUIRED ###")
            self.logger.warning("Phone verification required but no phone number available")
            self.logger.warning("Please complete verification manually or provide phone number")

            # Wait for manual completion
            self._wait_for_verification_completion()
            return True

        except Exception as e:
            self.logger.error(f"Error handling phone verification without number: {str(e)}")
            return True

    def _handle_alternative_verification_methods(self):
        """Handle alternative verification methods"""
        try:
            self.logger.info("Checking for alternative verification methods...")

            # Look for recovery email option
            recovery_email_indicators = [
                "recovery email",
                "backup email",
                "email de récupération",
                "adresse e-mail de récupération"
            ]

            for indicator in recovery_email_indicators:
                if self.check_js(f'"{indicator}"'):
                    self.logger.info("Recovery email option detected")
                    return self._handle_recovery_email_verification()

            # Look for security questions
            security_question_indicators = [
                "security question",
                "question de sécurité",
                "when did you create",
                "quand avez-vous créé"
            ]

            for indicator in security_question_indicators:
                if self.check_js(f'"{indicator}"'):
                    self.logger.info("Security question detected")
                    return self._handle_security_question()

            self.logger.warning("No alternative verification methods found")
            return True

        except Exception as e:
            self.logger.error(f"Error handling alternative verification: {str(e)}")
            return True

    def _handle_recovery_email_verification(self):
        """Handle recovery email verification"""
        try:
            self.logger.info("Attempting recovery email verification...")

            # Get recovery email from account data
            recovery_email = self._get_account_recovery_email()

            if recovery_email:
                self.logger.info(f"Using recovery email: {recovery_email}")

                # Try to find and click recovery email option
                recovery_buttons = [
                    f'//div[contains(text(), "{recovery_email}")]',
                    '//div[contains(@class, "recovery")]//button',
                    '//*[contains(text(), "Send")]',
                    '//*[contains(text(), "Envoyer")]'
                ]

                for button_xpath in recovery_buttons:
                    try:
                        button = self.browser.find_xpath(button_xpath)
                        if button:
                            if hasattr(self.browser, 'human_click_element'):
                                self.browser.human_click_element(button)
                            else:
                                button.click()

                            self.logger.info("Recovery email verification initiated")
                            sleep(uniform(3.0, 5.0))

                            self.logger.warning("### CHECK RECOVERY EMAIL ###")
                            self.logger.warning(f"Please check {recovery_email} for verification code")

                            # Wait for manual completion
                            self._wait_for_verification_completion()
                            return True
                    except:
                        continue

            self.logger.warning("Could not initiate recovery email verification")
            return True

        except Exception as e:
            self.logger.error(f"Error handling recovery email verification: {str(e)}")
            return True

    def _get_account_recovery_email(self):
        """Get recovery email from account data"""
        try:
            with open(gmail_map_file, 'r') as f:
                data = json.load(f)

            for account in data:
                if account.get('email') == self.browser.email:
                    recovery = account.get('email_conf', '')
                    if recovery and '@' in recovery:  # Make sure it's an email
                        return recovery

            return None

        except Exception as e:
            self.logger.error(f"Error getting recovery email: {str(e)}")
            return None

    def _detect_two_factor_auth(self):
        """Detect 2FA/MFA challenges"""
        try:
            current_url = self.browser.this_url()

            # Check URL patterns for 2FA
            tfa_patterns = [
                "signin/v2/challenge/totp",
                "signin/challenge/totp",
                "accounts.google.com/signin/v2/challenge/az",
                "signin/challenge/az"
            ]

            if any(pattern in current_url for pattern in tfa_patterns):
                return True

            # Check for 2FA text indicators
            tfa_indicators = [
                "Enter the code from your authenticator app",
                "2-Step Verification",
                "verification code",
                "authenticator",
                "Google Authenticator"
            ]

            for indicator in tfa_indicators:
                if self.check_js(f'"{indicator}"'):
                    return True

            return False

        except Exception as e:
            self.logger.error(f"Error detecting 2FA: {str(e)}")
            return False

    def _handle_two_factor_auth(self):
        """Handle 2FA challenges"""
        self.logger.warning("### 2FA VERIFICATION REQUIRED ###")
        self.logger.warning("Two-factor authentication detected")
        self.update_email_status(self.browser.email, "2fa_required")

        self.logger.warning("### MANUAL INTERVENTION REQUIRED ###")
        self.logger.warning("Please complete 2FA verification manually")

        # Wait for manual completion
        self._wait_for_verification_completion()
        return True

    def _detect_suspicious_activity(self):
        """Detect suspicious activity warnings including critical security alerts"""
        try:
            # Check current URL for security notifications page
            current_url = self.browser.this_url()
            if "myaccount.google.com/notifications" in current_url:
                self.logger.warning("Detected security notifications page - checking for alerts")
                return True

            # Check for critical security alert popup first (highest priority)
            critical_alert_selectors = [
                # French security alert elements (after language change)
                "//div[contains(text(), 'Alerte de sécurité critique')]",
                "//div[contains(text(), 'Activité suspecte détectée')]",
                "//div[contains(text(), 'Tentative suspecte de se connecter')]",
                "//button[contains(text(), 'Vous voyez une activité inhabituelle?')]",
                "//span[contains(text(), 'Oui, c\\'était moi')]",
                "//button[.//span[contains(text(), 'Oui, c\\'était moi')]]",
                # English security alert elements
                "//div[contains(text(), 'Critical security alert')]",
                "//div[contains(text(), 'Suspicious attempt to sign in')]",
                "//button[contains(text(), 'Check activity')]",
                "//button[contains(text(), 'Yes, it was me')]",
                # Generic security alert indicators
                "//div[contains(@class, 'security-alert')]",
                "//div[contains(@class, 'critical-alert')]",
                "//div[contains(@role, 'alert')]",
                # Specific selectors from your HTML
                "//div[@class='VXxIHd' and contains(text(), 'Alerte de sécurité critique')]",
                "//div[@class='I7nwS' and contains(text(), 'Activité suspecte détectée')]"
            ]

            for selector in critical_alert_selectors:
                try:
                    element = self.browser.find_xpath(selector)
                    if element and element.is_displayed():
                        self.logger.warning(f"Critical security alert detected via selector: {selector}")
                        return True
                except:
                    continue

            # Check for text-based indicators in page content
            suspicious_indicators = [
                # French indicators (after language change)
                "Alerte de sécurité critique",
                "Activité suspecte détectée",
                "Tentative suspecte de se connecter",
                "activité suspecte",
                "activité inhabituelle",
                "Vous voyez une activité inhabituelle",
                "Reconnaissez-vous cette activité",
                # English indicators
                "Critical security alert",
                "Suspicious attempt to sign in",
                "suspicious activity",
                "unusual activity",
                "We noticed something unusual",
                "Check activity",
                "Do you recognize this activity"
            ]

            for indicator in suspicious_indicators:
                if self.check_js(f'"{indicator}"'):
                    self.logger.warning(f"Suspicious activity detected via text: {indicator}")
                    return True

            return False

        except Exception as e:
            self.logger.error(f"Error detecting suspicious activity: {str(e)}")
            return False

    def _handle_suspicious_activity(self):
        """Handle suspicious activity warnings including critical security alerts"""
        self.logger.warning("### SUSPICIOUS ACTIVITY DETECTED ###")
        self.logger.warning("Google detected suspicious activity")
        self.update_email_status(self.browser.email, "suspicious_activity")

        # Check if we've already cleared suspicious activity for this account
        email = self.browser.email
        if email and self._is_suspicious_activity_cleared(email):
            self.logger.info("Suspicious activity already cleared for this account, attempting to continue")
            return False  # Continue with human actions

        # Check if we're on the security notifications page
        current_url = self.browser.this_url()
        if "myaccount.google.com/notifications" in current_url:
            self.logger.info("Handling security notifications page")
            if self._handle_security_notifications_page():
                self.logger.info("Security notifications page handled successfully")
                return False  # Continue with human actions
            else:
                self.logger.warning("Could not handle security notifications page")

        # First, try to handle critical security alert popup specifically
        if self._handle_critical_security_alert():
            self.logger.info("Critical security alert handled successfully")
            return False  # Continue with human actions

        # Try to click "This was me" or similar buttons for general suspicious activity
        confirmation_buttons = [
            # French confirmation buttons (after language change)
            '//button[.//span[contains(text(), "Oui, c\'était moi")]]',
            '//span[contains(text(), "Oui, c\'était moi")]/ancestor::button',
            '//button[contains(text(), "Oui, c\'était moi")]',
            '//button[contains(text(), "C\'était moi")]',
            # English confirmation buttons
            '//button[contains(text(), "This was me")]',
            '//button[contains(text(), "Yes, it was me")]',
            # Specific selector from your HTML
            '//button[@jsname="j6LnYe" and .//span[contains(text(), "Oui, c\'était moi")]]'
        ]

        for button_xpath in confirmation_buttons:
            try:
                button = self.browser.find_xpath(button_xpath)
                if button and button.is_displayed():
                    self.logger.info(f"Clicking confirmation button: {button_xpath}")
                    if hasattr(self.browser, 'human_click_element'):
                        self.browser.human_click_element(button)
                    else:
                        button.click()

                    sleep(uniform(2.0, 4.0))

                    # Mark suspicious activity as cleared since we successfully handled it
                    self._mark_suspicious_activity_cleared()

                    return False  # Continue with human actions
            except:
                continue

        # If no confirmation button found, wait for manual intervention
        self.logger.warning("### MANUAL INTERVENTION REQUIRED ###")
        self._wait_for_verification_completion()
        return True

    def _handle_security_notifications_page(self):
        """
        Handle the Google Account security notifications page that appears after language change.
        This page shows recent security activity and asks for confirmation.
        """
        try:
            self.logger.info("Handling Google Account security notifications page...")

            # Wait for page to load completely
            sleep(uniform(3.0, 5.0))

            # First, try to click "Vous voyez une activité inhabituelle?" button if present
            unusual_activity_buttons = [
                '//button[.//span[contains(text(), "Vous voyez une activité inhabituelle")]]',
                '//span[contains(text(), "Vous voyez une activité inhabituelle")]/ancestor::button',
                '//button[contains(text(), "Vous voyez une activité inhabituelle")]'
            ]

            for button_selector in unusual_activity_buttons:
                try:
                    button = self.browser.find_xpath(button_selector)
                    if button and button.is_displayed():
                        self.logger.info(f"Found 'unusual activity' button: {button_selector}")

                        if hasattr(self.browser, 'human_click_element'):
                            self.browser.human_click_element(button)
                        else:
                            button.click()

                        sleep(uniform(2.0, 4.0))
                        break
                except:
                    continue

            # Look for and click on suspicious activity alerts to review them
            suspicious_activity_links = [
                '//a[contains(@href, "notifications/eid/") and .//div[contains(text(), "Activité suspecte détectée")]]',
                '//a[@jsname="cDqwkb" and .//div[contains(text(), "Activité suspecte")]]',
                '//li[@class="Tti8Vd VfPpkd-ksKsZd-XxIAqe"]//a[contains(@href, "notifications")]'
            ]

            for link_selector in suspicious_activity_links:
                try:
                    link = self.browser.find_xpath(link_selector)
                    if link and link.is_displayed():
                        self.logger.info(f"Found suspicious activity link: {link_selector}")

                        if hasattr(self.browser, 'human_click_element'):
                            self.browser.human_click_element(link)
                        else:
                            link.click()

                        sleep(uniform(3.0, 5.0))

                        # After clicking the link, handle the detailed security page
                        if self._handle_detailed_security_activity_page():
                            return True
                        break
                except:
                    continue

            # If no specific alerts found, look for general confirmation buttons
            confirmation_buttons = [
                # French confirmation buttons from your HTML
                '//button[@jsname="j6LnYe" and .//span[contains(text(), "Oui, c\'était moi")]]',
                '//button[.//span[contains(text(), "Oui, c\'était moi")]]',
                '//span[contains(text(), "Oui, c\'était moi")]/ancestor::button',
                '//button[contains(text(), "Oui, c\'était moi")]',
                # English equivalents
                '//button[contains(text(), "Yes, it was me")]',
                '//button[contains(text(), "This was me")]'
            ]

            for button_selector in confirmation_buttons:
                try:
                    button = self.browser.find_xpath(button_selector)
                    if button and button.is_displayed():
                        self.logger.info(f"Found confirmation button: {button_selector}")

                        if hasattr(self.browser, 'human_click_element'):
                            self.browser.human_click_element(button)
                        else:
                            button.click()

                        sleep(uniform(2.0, 4.0))
                        self.logger.info("Clicked confirmation button on security notifications page")
                        return True
                except:
                    continue

            # If we can't find specific buttons, try to navigate back to Gmail
            self.logger.info("No specific security buttons found, attempting to navigate back to Gmail")
            self.browser.go("https://mail.google.com/mail/u/0/#inbox")
            sleep(uniform(3.0, 5.0))
            return True

        except Exception as e:
            self.logger.error(f"Error handling security notifications page: {str(e)}")
            return False

    def _handle_detailed_security_activity_page(self):
        """
        Handle the detailed security activity page that appears after clicking on a specific alert.
        This page shows details about the suspicious activity and asks for confirmation.
        """
        try:
            self.logger.info("Handling detailed security activity page...")

            # Wait for detailed page to load
            sleep(uniform(3.0, 5.0))

            # Look for the main confirmation buttons on the detailed page
            confirmation_buttons = [
                # French confirmation buttons (most likely after language change)
                '//button[.//span[contains(text(), "Oui, c\'était moi")]]',
                '//button[@jsname="j6LnYe" and .//span[contains(text(), "Oui, c\'était moi")]]',
                '//span[contains(text(), "Oui, c\'était moi")]/ancestor::button',
                '//button[contains(text(), "Oui, c\'était moi")]',
                '//button[contains(text(), "C\'était moi")]',

                # Look for buttons with specific classes from your HTML
                '//button[@class="VfPpkd-LgbsSe VfPpkd-LgbsSe-OWXEXe-INsAgc VfPpkd-LgbsSe-OWXEXe-Bz112c-M1Soyc Rj2Mlf OLiIxf PDpWxe LQeN7 SdOXCb uz4FJf VmUnYb cOqjte" and .//span[contains(text(), "Oui, c\'était moi")]]',

                # English confirmation buttons
                '//button[contains(text(), "Yes, it was me")]',
                '//button[contains(text(), "This was me")]',
                '//button[contains(text(), "It was me")]',

                # Generic confirmation patterns
                '//button[contains(@aria-label, "confirm") or contains(@aria-label, "confirmer")]',
                '//button[.//i[contains(@class, "done")] and .//span[contains(text(), "Oui")]]'  # Button with checkmark icon
            ]

            for button_selector in confirmation_buttons:
                try:
                    button = self.browser.find_xpath(button_selector)
                    if button and button.is_displayed():
                        self.logger.info(f"Found detailed page confirmation button: {button_selector}")

                        # Get button details for logging
                        button_text = button.text.strip()
                        self.logger.info(f"Button text: '{button_text}'")

                        if hasattr(self.browser, 'human_click_element'):
                            self.browser.human_click_element(button)
                        else:
                            button.click()

                        sleep(uniform(3.0, 5.0))
                        self.logger.info("Clicked confirmation button on detailed security page")

                        # After confirmation, we might be redirected back to notifications or Gmail
                        current_url = self.browser.this_url()
                        self.logger.info(f"After confirmation, current URL: {current_url}")

                        # If still on notifications page, try to navigate to Gmail
                        if "notifications" in current_url:
                            sleep(uniform(2.0, 3.0))
                            self.browser.go("https://mail.google.com/mail/u/0/#inbox")
                            sleep(uniform(3.0, 5.0))

                        return True
                except Exception as e:
                    self.logger.debug(f"Confirmation button {button_selector} failed: {str(e)}")
                    continue

            # If no confirmation buttons found, look for "Non, sécuriser le compte" (No, secure account)
            # and avoid clicking it, or look for other navigation options
            self.logger.warning("No confirmation buttons found on detailed security page")

            # Try to navigate back to Gmail as fallback
            self.browser.go("https://mail.google.com/mail/u/0/#inbox")
            sleep(uniform(3.0, 5.0))
            return True

        except Exception as e:
            self.logger.error(f"Error handling detailed security activity page: {str(e)}")
            return False

    def _check_and_clear_security_notifications(self):
        """
        Proactively visit the Google Account security notifications page to clear any suspicious activity alerts.
        This should be called after language change to prevent security popups during login.
        Note: This always checks the page - suspicious activity can occur at any time regardless of previous clearings.
        """
        try:
            email = self.browser.email

            # Log if suspicious activity was previously cleared, but still check for new activity
            if email and self._is_suspicious_activity_cleared(email):
                self.logger.info(f"Suspicious activity was previously cleared for {email}, but checking for new activity...")

            self.logger.info("Proactively checking Google Account security notifications...")

            # Navigate to the security notifications page
            notifications_url = "https://myaccount.google.com/notifications?origin=3&utm_source=sign_in_no_continue"
            self.browser.go(notifications_url)

            # Wait for page to load
            sleep(uniform(4.0, 6.0))

            # Check if we successfully reached the notifications page
            current_url = self.browser.this_url()
            if "notifications" not in current_url:
                self.logger.warning(f"Failed to reach notifications page, current URL: {current_url}")
                return False

            self.logger.info("Successfully reached security notifications page")

            # Look for the "Vous voyez une activité inhabituelle?" button first
            unusual_activity_handled = False
            unusual_activity_buttons = [
                '//button[.//span[contains(text(), "Vous voyez une activité inhabituelle")]]',
                '//span[contains(text(), "Vous voyez une activité inhabituelle")]/ancestor::button',
                '//button[contains(text(), "Vous voyez une activité inhabituelle")]'
            ]

            for button_selector in unusual_activity_buttons:
                try:
                    button = self.browser.find_xpath(button_selector)
                    if button and button.is_displayed():
                        self.logger.info(f"Found 'unusual activity' button, clicking: {button_selector}")

                        if hasattr(self.browser, 'human_click_element'):
                            self.browser.human_click_element(button)
                        else:
                            button.click()

                        sleep(uniform(2.0, 4.0))
                        unusual_activity_handled = True
                        break
                except:
                    continue

            # Look for and handle any critical security alerts
            critical_alerts_handled = 0
            critical_alert_selectors = [
                # French critical security alerts
                '//a[contains(@href, "notifications/eid/") and .//div[contains(text(), "Activité suspecte détectée")]]',
                '//a[@jsname="cDqwkb" and .//div[contains(text(), "Activité suspecte")]]',
                '//li[@class="Tti8Vd VfPpkd-ksKsZd-XxIAqe"]//a[contains(@href, "notifications")]',
                # English equivalents
                '//a[contains(@href, "notifications/eid/") and .//div[contains(text(), "Suspicious activity detected")]]'
            ]

            for alert_selector in critical_alert_selectors:
                try:
                    alerts = self.browser.find_xpaths(alert_selector)
                    for alert in alerts:
                        if alert and alert.is_displayed():
                            self.logger.info(f"Found critical security alert, clicking: {alert_selector}")

                            if hasattr(self.browser, 'human_click_element'):
                                self.browser.human_click_element(alert)
                            else:
                                alert.click()

                            sleep(uniform(3.0, 5.0))

                            # Handle the detailed security page that opens
                            if self._handle_detailed_security_activity_page():
                                critical_alerts_handled += 1
                                self.logger.info(f"Successfully handled critical security alert #{critical_alerts_handled}")

                                # Navigate back to notifications page for any remaining alerts
                                self.browser.go(notifications_url)
                                sleep(uniform(2.0, 3.0))
                            else:
                                self.logger.warning("Failed to handle detailed security activity page")

                            break  # Handle one alert at a time
                except Exception as e:
                    self.logger.debug(f"Alert selector {alert_selector} failed: {str(e)}")
                    continue

            # Look for any general confirmation buttons on the main notifications page
            general_confirmations_handled = 0
            confirmation_buttons = [
                # French confirmation buttons
                '//button[.//span[contains(text(), "Oui, c\'était moi")]]',
                '//button[@jsname="j6LnYe" and .//span[contains(text(), "Oui, c\'était moi")]]',
                '//span[contains(text(), "Oui, c\'était moi")]/ancestor::button',
                # English confirmation buttons
                '//button[contains(text(), "Yes, it was me")]',
                '//button[contains(text(), "This was me")]'
            ]

            for button_selector in confirmation_buttons:
                try:
                    buttons = self.browser.find_xpaths(button_selector)
                    for button in buttons:
                        if button and button.is_displayed():
                            self.logger.info(f"Found general confirmation button, clicking: {button_selector}")

                            if hasattr(self.browser, 'human_click_element'):
                                self.browser.human_click_element(button)
                            else:
                                button.click()

                            sleep(uniform(2.0, 4.0))
                            general_confirmations_handled += 1
                            break  # Handle one button at a time
                except:
                    continue

            # Summary of actions taken
            total_actions = (1 if unusual_activity_handled else 0) + critical_alerts_handled + general_confirmations_handled
            self.logger.info(f"Security notifications check completed:")
            self.logger.info(f"  - Unusual activity button: {'✓' if unusual_activity_handled else '✗'}")
            self.logger.info(f"  - Critical alerts handled: {critical_alerts_handled}")
            self.logger.info(f"  - General confirmations: {general_confirmations_handled}")
            self.logger.info(f"  - Total actions taken: {total_actions}")

            # Only mark suspicious activity as cleared if we actually found and handled suspicious activity
            if total_actions > 0:
                self.logger.info("Suspicious activity was found and handled, marking as cleared")
                self._mark_suspicious_activity_cleared()
            else:
                self.logger.info("No suspicious activity found on security notifications page")

            # Wait a bit before returning
            sleep(uniform(2.0, 3.0))

            return True  # Always return True - we successfully checked the page

        except Exception as e:
            self.logger.error(f"Error checking and clearing security notifications: {str(e)}")
            return False

    def _mark_suspicious_activity_cleared(self):
        """Mark that suspicious activity has been cleared for this account"""
        try:
            email = self.browser.email
            if not email:
                self.logger.warning("No email available to mark suspicious activity cleared")
                return

            # Update profile details to include suspicious activity cleared status
            profile_details = self.get_profile_details(email)
            if profile_details:
                profile_details['suspicious_activity_cleared'] = True
                profile_details['suspicious_activity_cleared_date'] = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
                self.update_profile_details(email, profile_details)
                self.logger.info(f"Marked suspicious activity as cleared for {email}")
            else:
                self.logger.warning(f"Could not get profile details to mark suspicious activity cleared for {email}")

        except Exception as e:
            self.logger.error(f"Error marking suspicious activity cleared: {str(e)}")

    def _is_suspicious_activity_cleared(self, email, check_recent=False):
        """
        Check if suspicious activity has already been cleared for this account

        Args:
            email: The email address to check
            check_recent: If True, only consider it cleared if it was within the last 24 hours
        """
        try:
            profile_details = self.get_profile_details(email)
            if profile_details:
                cleared = profile_details.get('suspicious_activity_cleared', False)
                if cleared:
                    cleared_date_str = profile_details.get('suspicious_activity_cleared_date', 'Unknown')

                    if check_recent and cleared_date_str != 'Unknown':
                        try:
                            from datetime import datetime
                            cleared_date = datetime.strptime(cleared_date_str, '%Y-%m-%d %H:%M:%S')
                            hours_since_cleared = (datetime.now() - cleared_date).total_seconds() / 3600

                            if hours_since_cleared > 24:  # More than 24 hours ago
                                self.logger.info(f"Suspicious activity was cleared {hours_since_cleared:.1f} hours ago for {email}, may need re-checking")
                                return False
                        except:
                            # If we can't parse the date, assume it needs re-checking
                            return False

                    self.logger.info(f"Suspicious activity cleared for {email} on {cleared_date_str}")
                    return True
            return False
        except Exception as e:
            self.logger.error(f"Error checking suspicious activity cleared status: {str(e)}")
            return False

    def _handle_critical_security_alert(self):
        """
        Handle the specific 'Critical security alert' popup that appears when Google
        detects a suspicious sign-in attempt. This popup has a red background with
        an exclamation mark and a 'Check activity' button.
        """
        try:
            self.logger.info("Attempting to handle critical security alert popup...")

            # First, try to find and click the "Check activity" button
            check_activity_buttons = [
                '//button[contains(text(), "Check activity")]',
                '//button[contains(text(), "Vérifier l\'activité")]',
                '//button[contains(text(), "Check Activity")]',
                '//button[contains(text(), "CHECK ACTIVITY")]',
                # Alternative selectors for the button
                '//button[contains(@class, "security-alert")]',
                '//button[contains(@class, "critical-alert")]',
                # CSS selectors as backup
                'button[data-action="check-activity"]',
                'button[aria-label*="Check activity"]',
                'button[aria-label*="Vérifier"]'
            ]

            for button_selector in check_activity_buttons:
                try:
                    if button_selector.startswith('//'):
                        # XPath selector
                        button = self.browser.find_xpath(button_selector)
                    else:
                        # CSS selector
                        button = self.browser.find_css(button_selector)

                    if button and button.is_displayed():
                        self.logger.info(f"Found 'Check activity' button: {button_selector}")

                        # Click the button using human-like interaction if available
                        if hasattr(self.browser, 'human_click_element'):
                            self.browser.human_click_element(button)
                        else:
                            button.click()

                        sleep(uniform(2.0, 4.0))

                        # After clicking, we might be redirected to account security page
                        # Wait a moment and then try to navigate back or handle the next step
                        current_url = self.browser.this_url()
                        self.logger.info(f"After clicking 'Check activity', current URL: {current_url}")

                        # If we're on a security review page, try to confirm the activity
                        if self._handle_security_review_page():
                            self.logger.info("Security review page handled successfully")

                        return True

                except Exception as e:
                    self.logger.debug(f"Button selector {button_selector} failed: {str(e)}")
                    continue

            # If no "Check activity" button found, try to close the popup
            close_buttons = [
                '//button[@aria-label="Close"]',
                '//button[@aria-label="Fermer"]',
                '//button[contains(@class, "close")]',
                '//span[contains(@class, "close")]',
                # X button selectors
                '//button[text()="×"]',
                '//span[text()="×"]'
            ]

            for close_selector in close_buttons:
                try:
                    close_button = self.browser.find_xpath(close_selector)
                    if close_button and close_button.is_displayed():
                        self.logger.info(f"Found close button for security alert: {close_selector}")

                        if hasattr(self.browser, 'human_click_element'):
                            self.browser.human_click_element(close_button)
                        else:
                            close_button.click()

                        sleep(uniform(1.0, 2.0))
                        return True

                except Exception as e:
                    self.logger.debug(f"Close button selector {close_selector} failed: {str(e)}")
                    continue

            # If we can't find specific buttons, try pressing Escape key
            try:
                self.logger.info("Trying to dismiss security alert with Escape key")
                from selenium.webdriver.common.keys import Keys
                self.browser.find_xpath('//body').send_keys(Keys.ESCAPE)
                sleep(uniform(1.0, 2.0))
                return True
            except:
                pass

            self.logger.warning("Could not find any buttons to handle critical security alert")
            return False

        except Exception as e:
            self.logger.error(f"Error handling critical security alert: {str(e)}")
            return False

    def _handle_security_review_page(self):
        """
        Handle the security review page that appears after clicking 'Check activity'.
        This page typically asks the user to confirm if the sign-in attempt was legitimate.
        """
        try:
            self.logger.info("Handling security review page...")

            # Wait a moment for the page to load
            sleep(uniform(2.0, 3.0))

            # Look for confirmation buttons that indicate the sign-in was legitimate
            confirmation_buttons = [
                # English variations
                '//button[contains(text(), "Yes, it was me")]',
                '//button[contains(text(), "This was me")]',
                '//button[contains(text(), "It was me")]',
                '//button[contains(text(), "Confirm")]',
                '//button[contains(text(), "Yes")]',
                # French variations
                '//button[contains(text(), "Oui, c\'était moi")]',
                '//button[contains(text(), "C\'était moi")]',
                '//button[contains(text(), "Confirmer")]',
                '//button[contains(text(), "Oui")]',
                # Generic confirmation patterns
                '//button[contains(@data-action, "confirm")]',
                '//button[contains(@aria-label, "confirm")]',
                '//button[contains(@class, "confirm")]'
            ]

            for button_selector in confirmation_buttons:
                try:
                    button = self.browser.find_xpath(button_selector)
                    if button and button.is_displayed():
                        self.logger.info(f"Found confirmation button: {button_selector}")

                        if hasattr(self.browser, 'human_click_element'):
                            self.browser.human_click_element(button)
                        else:
                            button.click()

                        sleep(uniform(2.0, 4.0))
                        self.logger.info("Security review confirmation completed")
                        return True

                except Exception as e:
                    self.logger.debug(f"Confirmation button {button_selector} failed: {str(e)}")
                    continue

            # If no confirmation buttons found, look for "Continue" or "Next" buttons
            continue_buttons = [
                '//button[contains(text(), "Continue")]',
                '//button[contains(text(), "Continuer")]',
                '//button[contains(text(), "Next")]',
                '//button[contains(text(), "Suivant")]',
                '//button[contains(text(), "Proceed")]',
                '//button[contains(text(), "Procéder")]'
            ]

            for button_selector in continue_buttons:
                try:
                    button = self.browser.find_xpath(button_selector)
                    if button and button.is_displayed():
                        self.logger.info(f"Found continue button: {button_selector}")

                        if hasattr(self.browser, 'human_click_element'):
                            self.browser.human_click_element(button)
                        else:
                            button.click()

                        sleep(uniform(2.0, 4.0))
                        self.logger.info("Security review continue button clicked")
                        return True

                except Exception as e:
                    self.logger.debug(f"Continue button {button_selector} failed: {str(e)}")
                    continue

            # Check if we're already past the security review (successful redirect)
            current_url = self.browser.this_url()
            if any(pattern in current_url for pattern in ['gmail.com', 'google.com', 'myaccount.google.com']):
                self.logger.info("Security review appears to be completed (redirected to main service)")
                return True

            self.logger.warning("No confirmation buttons found on security review page")
            return False

        except Exception as e:
            self.logger.error(f"Error handling security review page: {str(e)}")
            return False

    def _should_change_language_to_french(self):
        """
        Check if we should change the account language to French.
        Returns True if this is the first login or language hasn't been changed yet.
        """
        try:
            # Check if language_changed flag exists in Gmail accounts map
            with open(gmail_map_file, 'r') as f:
                data = json.load(f)

            for item in data:
                if item['email'] == self.browser.email:
                    # If language_changed field doesn't exist or is False, we should change it
                    return not item.get('language_changed', False)

            # If account not found in map, assume first login
            return True

        except Exception as e:
            self.logger.error(f"Error checking language change status: {str(e)}")
            # Default to changing language if we can't determine status
            return True

    def _update_language_changed_status(self, email, status=True):
        """
        Update the language_changed status in the Gmail accounts map.

        Args:
            email (str): Email address
            status (bool): Language changed status (default: True)
        """
        try:
            with open(gmail_map_file, 'r') as f:
                data = json.load(f)

            # Find and update the account
            for item in data:
                if item['email'] == email:
                    item['language_changed'] = status
                    break
            else:
                # Account not found, add it
                data.append({
                    'email': email,
                    'password': '',  # Will be updated elsewhere
                    'ua': '',
                    'email_conf': '',
                    'phone': '',
                    'status': 'active',
                    'language_changed': status
                })

            # Save updated data
            with open(gmail_map_file, 'w') as f:
                json.dump(data, f, indent=4)

            self.logger.info(f"Updated language_changed status for {email}: {status}")

            # Also update profile configuration if enhanced driver is available
            try:
                if hasattr(self, 'browser') and hasattr(self.browser, 'profile_manager'):
                    profile_id = self.browser.profile_manager._generate_profile_id(email)
                    if profile_id in self.browser.profile_manager.profiles_config:
                        profile = self.browser.profile_manager.profiles_config[profile_id]
                        if 'account_settings' not in profile:
                            profile['account_settings'] = {}
                        profile['account_settings']['language_changed_to_french'] = status
                        profile['account_settings']['language_change_date'] = datetime.now().isoformat()
                        self.browser.profile_manager._save_profiles_config()
                        self.logger.info(f"Updated profile configuration for language change: {email}")
            except Exception as profile_error:
                self.logger.debug(f"Could not update profile configuration: {str(profile_error)}")

        except Exception as e:
            self.logger.error(f"Error updating language changed status: {str(e)}")

    def _change_gmail_language_to_french(self):
        """
        Change Google account language to French using the direct Google Account language settings page.
        This is more reliable than trying to change it through Gmail settings.
        """
        try:
            self.logger.info("Starting Google account language change to French...")

            # Save current URL to return to later
            original_url = self.browser.this_url()

            # Navigate to Google Account language settings page
            language_settings_url = "https://myaccount.google.com/language"
            self.browser.go(language_settings_url)
            sleep(uniform(3.0, 5.0))

            # Wait for language settings page to load
            try:
                self.browser.wait_xpath_presence('//button[contains(@aria-label, "Edit") or contains(@aria-label, "تعديل")]', timeout=15)
                self.logger.info("Language settings page loaded successfully")
            except:
                try:
                    # Alternative wait - look for language input or button
                    self.browser.wait_xpath_presence('//input[@role="combobox"]', timeout=10)
                    self.logger.info("Language input field found")
                except:
                    self.logger.warning("Language settings page may not have loaded completely, continuing...")

            # Step 1: Click the edit language button
            edit_button_found = self._click_edit_language_button()

            if edit_button_found:
                # Step 2: Enter "Français" in the language input field
                language_input_success = self._enter_french_language()

                if language_input_success:
                    # Step 3: Select French from the dropdown suggestions
                    french_selected = self._select_french_from_suggestions()

                    if french_selected:
                        # Step 4: Select a French-speaking country (excluding African countries)
                        country_selected = self._select_french_country()

                        if country_selected:
                            # Step 5: Save the language change
                            self._save_google_account_language_settings()

                            # Update the language_changed status
                            self._update_language_changed_status(self.browser.email, True)

                            self.logger.info("Google account language change to French completed successfully")
                        else:
                            self.logger.warning("Could not select French country, but proceeding to save")
                            self._save_google_account_language_settings()
                            self._update_language_changed_status(self.browser.email, True)
                    else:
                        self.logger.warning("Could not select French from suggestions")
                        self._update_language_changed_status(self.browser.email, True)  # Mark as attempted
                else:
                    self.logger.warning("Could not enter French in language input")
                    self._update_language_changed_status(self.browser.email, True)  # Mark as attempted
            else:
                self.logger.warning("Could not find edit language button")
                self._update_language_changed_status(self.browser.email, True)  # Mark as attempted

            # Wait a moment for settings to save
            sleep(uniform(2.0, 3.0))

            # Return to original URL or Gmail inbox
            if original_url and ('gmail.com' in original_url or 'google.com' in original_url):
                self.browser.go(original_url)
            else:
                self.browser.go("https://mail.google.com/mail/u/0/#inbox")

            sleep(uniform(2.0, 3.0))

        except Exception as e:
            self.logger.error(f"Error changing Google account language to French: {str(e)}")
            # Even if language change fails, mark as attempted to avoid repeated attempts
            self._update_language_changed_status(self.browser.email, True)

    def _click_edit_language_button(self):
        """
        Click the edit language button on Google Account language settings page.
        Handles both English and Arabic interfaces.
        """
        try:
            # Button selectors for different languages and states
            edit_button_selectors = [
                # English interface
                '//button[contains(@aria-label, "Edit language")]',
                '//button[contains(@aria-label, "Change language")]',
                # Arabic interface (from your example)
                '//button[contains(@aria-label, "تعديل اللغة")]',
                '//button[contains(@aria-label, "تغيير اللغة")]',
                # Generic edit button selectors
                '//button[contains(@class, "pYTkkf-Bz112c-LgbsSe")]',  # From your HTML
                '//button[@jsname="Pr7Yme"]',  # From your HTML
                # SVG edit icon button
                '//button[.//svg[contains(@class, "NMm5M")]]',
                # Fallback selectors
                '//button[contains(text(), "Edit")]',
                '//button[contains(text(), "تعديل")]'
            ]

            for selector in edit_button_selectors:
                try:
                    button = self.browser.find_xpath(selector)
                    if button and button.is_displayed():
                        self.logger.info(f"Found edit language button: {selector}")

                        if hasattr(self.browser, 'human_click_element'):
                            self.browser.human_click_element(button)
                        else:
                            button.click()

                        sleep(uniform(2.0, 3.0))
                        self.logger.info("Clicked edit language button successfully")
                        return True

                except Exception as e:
                    self.logger.debug(f"Edit button selector {selector} failed: {str(e)}")
                    continue

            self.logger.warning("Could not find edit language button")
            return False

        except Exception as e:
            self.logger.error(f"Error clicking edit language button: {str(e)}")
            return False

    def _enter_french_language(self):
        """
        Enter "Français" in the language input field.
        """
        try:
            # Input field selectors
            input_selectors = [
                # From your HTML example
                '//input[@role="combobox"]',
                '//input[@id="c5"]',
                '//input[@jsname="YPqjbf"]',
                '//input[@class="qdOxv-fmcmS-wGMbrd"]',
                # Generic language input selectors
                '//input[contains(@aria-label, "language")]',
                '//input[contains(@aria-label, "لغة")]',
                '//input[@data-axe="mdc-autocomplete"]'
            ]

            for selector in input_selectors:
                try:
                    input_field = self.browser.find_xpath(selector)
                    if input_field and input_field.is_displayed():
                        self.logger.info(f"Found language input field: {selector}")

                        # Clear the field first
                        input_field.clear()
                        sleep(uniform(0.5, 1.0))

                        # Type "Français"
                        if hasattr(self.browser, 'human_type_text'):
                            self.browser.human_type_text(input_field, "Français")
                        else:
                            input_field.send_keys("Français")

                        sleep(uniform(1.0, 2.0))
                        self.logger.info("Entered 'Français' in language input field")
                        return True

                except Exception as e:
                    self.logger.debug(f"Input selector {selector} failed: {str(e)}")
                    continue

            self.logger.warning("Could not find language input field")
            return False

        except Exception as e:
            self.logger.error(f"Error entering French language: {str(e)}")
            return False

    def _select_french_from_suggestions(self):
        """
        Select French from the dropdown suggestions that appear after typing.
        """
        try:
            # Wait for suggestions to appear
            sleep(uniform(1.0, 2.0))

            # Suggestion selectors for French
            french_suggestion_selectors = [
                # Look for "Français" in dropdown options
                '//li[contains(text(), "Français")]',
                '//div[contains(text(), "Français")]',
                '//span[contains(text(), "Français")]',
                # Look for French language options
                '//li[@role="option" and contains(text(), "Français")]',
                '//div[@role="option" and contains(text(), "Français")]',
                # Generic dropdown options containing French
                '//*[@role="option"][contains(., "Français")]',
                '//*[@role="listbox"]//*[contains(text(), "Français")]',
                # From your HTML structure
                '//ul[@role="listbox"]//li[contains(text(), "Français")]',
                '//ul[@class="W7g1Rb-rymPhb P3yA4d"]//li[contains(text(), "Français")]'
            ]

            for selector in french_suggestion_selectors:
                try:
                    suggestion = self.browser.find_xpath(selector)
                    if suggestion and suggestion.is_displayed():
                        self.logger.info(f"Found French suggestion: {selector}")

                        if hasattr(self.browser, 'human_click_element'):
                            self.browser.human_click_element(suggestion)
                        else:
                            suggestion.click()

                        sleep(uniform(1.0, 2.0))
                        self.logger.info("Selected French from suggestions")
                        return True

                except Exception as e:
                    self.logger.debug(f"French suggestion selector {selector} failed: {str(e)}")
                    continue

            # If no suggestions found, try pressing Enter to confirm
            try:
                from selenium.webdriver.common.keys import Keys
                input_field = self.browser.find_xpath('//input[@role="combobox"]')
                if input_field:
                    input_field.send_keys(Keys.ENTER)
                    sleep(uniform(1.0, 2.0))
                    self.logger.info("Pressed Enter to confirm French language selection")
                    return True
            except:
                pass

            self.logger.warning("Could not find French language suggestion")
            return False

        except Exception as e:
            self.logger.error(f"Error selecting French from suggestions: {str(e)}")
            return False

    def _select_french_country(self):
        """
        Select a French-speaking country from the dropdown that appears after selecting French.
        Randomly chooses from allowed countries (excluding Morocco, Algeria, and African countries).
        """
        try:
            self.logger.info("Selecting French-speaking country...")

            # Wait for country selection dropdown to appear
            sleep(uniform(2.0, 3.0))

            # Define allowed French-speaking countries (excluding African countries as requested)
            allowed_countries = [
                "France",
                "Canada",
                "Belgique",  # Belgium
                "Suisse",    # Switzerland
                "Luxembourg",
                "Monaco"
            ]

            # Define countries to exclude (African countries and specifically requested exclusions)
            excluded_countries = [
                "Maroc",           # Morocco
                "Algérie",         # Algeria
                "Tunisie",         # Tunisia
                "Sénégal",         # Senegal
                "Mali",
                "Burkina Faso",
                "Niger",
                "Tchad",           # Chad
                "République centrafricaine",
                "Cameroun",        # Cameroon
                "Gabon",
                "République du Congo",
                "République démocratique du Congo",
                "Burundi",
                "Rwanda",
                "Djibouti",
                "Comores",
                "Madagascar",
                "Maurice",         # Mauritius
                "Seychelles",
                "Bénin",           # Benin
                "Togo",
                "Côte d'Ivoire",   # Ivory Coast
                "Guinée",          # Guinea
                "Mauritanie"       # Mauritania
            ]

            # Look for country options in the dropdown
            country_selectors = [
                # Generic country option selectors
                '//li[@role="option"]',
                '//div[@role="option"]',
                '//span[@role="option"]',
                # From your screenshot structure
                '//div[contains(@class, "option")]',
                '//li[contains(@class, "option")]',
                # Dropdown list items
                '//*[@role="listbox"]//*[contains(@class, "option")]',
                '//*[@role="listbox"]//li',
                '//*[@role="listbox"]//div'
            ]

            available_countries = []

            # Find all available country options
            for selector in country_selectors:
                try:
                    # Use the browser's method to find multiple elements
                    if hasattr(self.browser, 'find_elements'):
                        elements = self.browser.find_elements(By.XPATH, selector)
                    else:
                        # Fallback: try to find individual elements
                        try:
                            element = self.browser.find_xpath(selector)
                            elements = [element] if element else []
                        except:
                            elements = []

                    for element in elements:
                        try:
                            if element.is_displayed():
                                country_text = element.text.strip()
                                if country_text and len(country_text) > 1:  # Valid country name
                                    available_countries.append((element, country_text))
                        except:
                            continue

                    if available_countries:
                        break  # Found countries, no need to try other selectors

                except Exception as e:
                    self.logger.debug(f"Country selector {selector} failed: {str(e)}")
                    continue

            if not available_countries:
                self.logger.warning("No country options found in dropdown")
                return False

            self.logger.info(f"Found {len(available_countries)} country options")

            # Filter countries: find allowed ones first, then exclude forbidden ones
            preferred_countries = []
            acceptable_countries = []

            for element, country_text in available_countries:
                self.logger.debug(f"Evaluating country option: {country_text}")

                # Check if it's in our preferred list
                if any(allowed.lower() in country_text.lower() for allowed in allowed_countries):
                    preferred_countries.append((element, country_text))
                    self.logger.debug(f"Added to preferred: {country_text}")
                # Check if it's NOT in excluded list
                elif not any(excluded.lower() in country_text.lower() for excluded in excluded_countries):
                    acceptable_countries.append((element, country_text))
                    self.logger.debug(f"Added to acceptable: {country_text}")
                else:
                    self.logger.debug(f"Excluded country: {country_text}")

            # Choose country: prefer allowed countries, then acceptable ones
            if preferred_countries:
                chosen_element, chosen_country = choice(preferred_countries)
                self.logger.info(f"Selected preferred French country: {chosen_country}")
            elif acceptable_countries:
                chosen_element, chosen_country = choice(acceptable_countries)
                self.logger.info(f"Selected acceptable French country: {chosen_country}")
            else:
                # Fallback: choose any non-excluded country
                non_excluded = [(el, text) for el, text in available_countries
                               if not any(excluded.lower() in text.lower() for excluded in excluded_countries)]
                if non_excluded:
                    chosen_element, chosen_country = choice(non_excluded)
                    self.logger.info(f"Selected fallback French country: {chosen_country}")
                else:
                    self.logger.warning("All available countries are excluded, selecting first available")
                    chosen_element, chosen_country = available_countries[0]

            # Click the chosen country
            if hasattr(self.browser, 'human_click_element'):
                self.browser.human_click_element(chosen_element)
            else:
                chosen_element.click()

            sleep(uniform(1.0, 2.0))
            self.logger.info(f"Successfully selected French country: {chosen_country}")
            return True

        except Exception as e:
            self.logger.error(f"Error selecting French country: {str(e)}")
            return False

    def _save_google_account_language_settings(self):
        """
        Save the Google Account language settings after selecting French.
        """
        try:
            # Look for save/confirm buttons with specific selectors from the actual HTML
            # IMPORTANT: Target the SAVE button specifically, not cancel or other buttons
            save_button_selectors = [
                # Most specific selectors for the SAVE button from your HTML
                '//button[@aria-label="حفظ اللغة التي اخترتَها"]',  # "Save the language you chose" in Arabic
                '//button[.//span[@jsname="V67aGc" and text()="حفظ"]]',  # Button with specific span containing "حفظ"
                '//button[.//span[text()="حفظ"]]',  # Button containing span with Arabic "Save" text
                '//span[text()="حفظ"]/ancestor::button',  # Find button that contains the "حفظ" span

                # Specific class and controller combinations for save button
                '//button[@class="mUIrbf-LgbsSe mUIrbf-LgbsSe-OWXEXe-dgl2Hf" and @jscontroller="O626Fe" and .//span[text()="حفظ"]]',
                '//button[@jscontroller="O626Fe" and .//span[text()="حفظ"]]',
                '//button[@data-mdc-dialog-action="x8hlje" and .//span[text()="حفظ"]]',

                # More specific Arabic save button selectors
                '//button[contains(@aria-label, "حفظ") and not(contains(@aria-label, "إلغاء"))]',  # Save but not Cancel
                '//button[.//span[contains(text(), "حفظ")] and not(.//span[contains(text(), "إلغاء")])]',  # Save span but not Cancel span

                # English equivalents with same specificity
                '//button[.//span[text()="Save"]]',
                '//button[@aria-label="Save your language choice"]',
                '//button[contains(@aria-label, "Save") and not(contains(@aria-label, "Cancel"))]',

                # French equivalents
                '//button[.//span[text()="Enregistrer"]]',
                '//button[@aria-label="Enregistrer votre choix de langue"]',
                '//button[contains(@aria-label, "Enregistrer") and not(contains(@aria-label, "Annuler"))]',

                # Generic save button texts (less specific, used as fallback)
                '//button[contains(text(), "Save") and not(contains(text(), "Cancel"))]',
                '//button[contains(text(), "حفظ") and not(contains(text(), "إلغاء"))]',  # Arabic Save but not Cancel
                '//button[contains(text(), "Enregistrer") and not(contains(text(), "Annuler"))]',  # French Save but not Cancel

                # Last resort generic selectors
                '//button[@type="submit"]',
                '//button[contains(@class, "save")]',
                '//button[contains(@id, "save")]'
            ]

            # Wait a bit longer for the save button to appear after country selection
            sleep(uniform(2.0, 4.0))

            for selector in save_button_selectors:
                try:
                    save_button = self.browser.find_xpath(selector)
                    if save_button and save_button.is_displayed():
                        # Get detailed information about the button
                        button_text = save_button.text.strip()
                        button_aria_label = save_button.get_attribute('aria-label') or ''
                        button_class = save_button.get_attribute('class') or ''
                        button_data_action = save_button.get_attribute('data-mdc-dialog-action') or ''

                        self.logger.info(f"Found potential save button with selector: {selector}")
                        self.logger.info(f"Button text: '{button_text}'")
                        self.logger.info(f"Button aria-label: '{button_aria_label}'")
                        self.logger.info(f"Button class: '{button_class}'")
                        self.logger.info(f"Button data-action: '{button_data_action}'")

                        # Verify this is actually the SAVE button and not cancel
                        is_save_button = False

                        # Check for Arabic save indicators
                        if 'حفظ' in button_text or 'حفظ' in button_aria_label:
                            # Make sure it's not cancel (إلغاء)
                            if 'إلغاء' not in button_text and 'إلغاء' not in button_aria_label:
                                is_save_button = True
                                self.logger.info("Confirmed: This is the Arabic SAVE button")

                        # Check for English save indicators
                        elif 'Save' in button_text or 'Save' in button_aria_label:
                            if 'Cancel' not in button_text and 'Cancel' not in button_aria_label:
                                is_save_button = True
                                self.logger.info("Confirmed: This is the English SAVE button")

                        # Check for French save indicators
                        elif 'Enregistrer' in button_text or 'Enregistrer' in button_aria_label:
                            if 'Annuler' not in button_text and 'Annuler' not in button_aria_label:
                                is_save_button = True
                                self.logger.info("Confirmed: This is the French SAVE button")

                        # If we can't determine by text, assume it's save if we found it with a specific selector
                        elif selector.startswith('//button[@aria-label="حفظ') or '//span[text()="حفظ"]' in selector:
                            is_save_button = True
                            self.logger.info("Confirmed: This is the SAVE button based on specific selector")

                        if not is_save_button:
                            self.logger.warning(f"Skipping button - appears to be cancel or other action, not save")
                            continue

                        # Scroll to button if needed
                        try:
                            self.browser.execute_js("arguments[0].scrollIntoView(true);", save_button)
                            sleep(uniform(0.5, 1.0))
                        except:
                            pass

                        # Try clicking the save button with multiple methods
                        click_success = False

                        # Method 1: Human-like click
                        if hasattr(self.browser, 'human_click_element'):
                            try:
                                self.browser.human_click_element(save_button)
                                click_success = True
                                self.logger.info("Clicked save button using human_click_element")
                            except Exception as e:
                                self.logger.debug(f"Human click failed: {str(e)}")

                        # Method 2: Regular click
                        if not click_success:
                            try:
                                save_button.click()
                                click_success = True
                                self.logger.info("Clicked save button using regular click")
                            except Exception as e:
                                self.logger.debug(f"Regular click failed: {str(e)}")

                        # Method 3: JavaScript click
                        if not click_success:
                            try:
                                self.browser.execute_js("arguments[0].click();", save_button)
                                click_success = True
                                self.logger.info("Clicked save button using JavaScript click")
                            except Exception as e:
                                self.logger.debug(f"JavaScript click failed: {str(e)}")

                        if click_success:
                            sleep(uniform(3.0, 5.0))  # Wait longer for save to process
                            self.logger.info("Successfully clicked save button for language settings")

                            # After language change, proactively visit security notifications to clear any alerts
                            # Note: This is separate from language tracking - suspicious activity can occur independently
                            self.logger.info("Language change completed, now checking security notifications...")
                            if self._check_and_clear_security_notifications():
                                self.logger.info("Security notifications checked and cleared successfully")
                                # Note: _mark_suspicious_activity_cleared() is called within _check_and_clear_security_notifications()
                                # only if suspicious activity was actually found and cleared
                            else:
                                self.logger.warning("Could not fully clear security notifications")

                            return True
                        else:
                            self.logger.warning(f"All click methods failed for save button: {selector}")
                            continue

                except Exception as e:
                    self.logger.debug(f"Save button selector {selector} failed: {str(e)}")
                    continue

            # If no save button found, the change might be auto-saved
            # Check if we're redirected or if the page has changed
            current_url = self.browser.this_url()
            if 'myaccount.google.com' in current_url:
                self.logger.info("Language change may have been auto-saved")
                return True

            # Try pressing Enter as a fallback
            try:
                from selenium.webdriver.common.keys import Keys
                self.browser.find_xpath('//body').send_keys(Keys.ENTER)
                sleep(uniform(1.0, 2.0))
                self.logger.info("Pressed Enter to save language settings")
                return True
            except:
                pass

            self.logger.warning("Could not find save button for language settings")
            return False

        except Exception as e:
            self.logger.error(f"Error saving Google Account language settings: {str(e)}")
            return False



    def _detect_account_recovery(self):
        """Detect account recovery challenges"""
        try:
            recovery_indicators = [
                "account recovery",
                "récupération de compte",
                "when did you create this account",
                "quand avez-vous créé ce compte",
                "last password you remember",
                "dernier mot de passe dont vous vous souvenez"
            ]

            for indicator in recovery_indicators:
                if self.check_js(f'"{indicator}"'):
                    return True

            return False

        except Exception as e:
            self.logger.error(f"Error detecting account recovery: {str(e)}")
            return False

    def _handle_account_recovery(self):
        """Handle account recovery challenges"""
        self.logger.warning("### ACCOUNT RECOVERY REQUIRED ###")
        self.logger.warning("Google is requesting account recovery information")
        self.update_email_status(self.browser.email, "recovery_required")

        self.logger.warning("### MANUAL INTERVENTION REQUIRED ###")
        self.logger.warning("Please complete account recovery manually")

        # Wait for manual completion
        self._wait_for_verification_completion()
        return True

    def _detect_terms_update(self):
        """Detect terms of service updates"""
        try:
            terms_indicators = [
                "Terms of Service",
                "Privacy Policy",
                "Conditions d'utilisation",
                "Règles de confidentialité",
                "I agree",
                "J'accepte",
                "Accept",
                "Accepter"
            ]

            for indicator in terms_indicators:
                if self.check_js(f'"{indicator}"'):
                    return True

            return False

        except Exception as e:
            self.logger.error(f"Error detecting terms update: {str(e)}")
            return False

    def _handle_terms_update(self):
        """Handle terms of service updates"""
        self.logger.info("### TERMS OF SERVICE UPDATE ###")
        self.logger.info("Google is requesting acceptance of updated terms")

        # Try to accept terms
        accept_buttons = [
            '//button[contains(text(), "I agree")]',
            '//button[contains(text(), "Accept")]',
            '//button[contains(text(), "J\'accepte")]',
            '//button[contains(text(), "Accepter")]',
            '//button[@type="submit"]'
        ]

        for button_xpath in accept_buttons:
            try:
                button = self.browser.find_xpath(button_xpath)
                if button:
                    if hasattr(self.browser, 'human_click_element'):
                        self.browser.human_click_element(button)
                    else:
                        button.click()

                    sleep(uniform(2.0, 4.0))
                    self.logger.info("Terms accepted successfully")
                    return False  # Continue with human actions
            except:
                continue

        # If no accept button found, wait for manual intervention
        self.logger.warning("### MANUAL INTERVENTION REQUIRED ###")
        self.logger.warning("Please accept terms of service manually")
        self._wait_for_verification_completion()
        return True

    def _handle_security_question(self):
        """Handle security questions"""
        self.logger.warning("### SECURITY QUESTION DETECTED ###")
        self.logger.warning("Google is asking a security question")
        self.update_email_status(self.browser.email, "security_question")

        self.logger.warning("### MANUAL INTERVENTION REQUIRED ###")
        self.logger.warning("Please answer the security question manually")

        # Wait for manual completion
        self._wait_for_verification_completion()
        return True

    def _explore_google_services(self):
        """Phase 1: Explore Google services to establish natural browsing patterns"""
        self.logger.info("Phase 1: Exploring Google services...")

        try:
            # Start from Google homepage
            self.browser.go("https://www.google.com")
            sleep(uniform(2.0, 4.0))

            # Simulate reading the page
            if hasattr(self.browser, 'scrol_down'):
                self.browser.scrol_down(randint(50, 100))

            sleep(uniform(1.5, 3.0))

            # Click on Google Apps menu (9 dots icon)
            try:
                apps_menu = self.browser.find_xpath('//a[@aria-label="Google apps"]')
                if hasattr(self.browser, 'human_click_element'):
                    self.browser.human_click_element(apps_menu)
                else:
                    apps_menu.click()

                sleep(uniform(2.0, 3.5))

                # Look at available apps (simulate human curiosity)
                if hasattr(self.browser, 'scrol_down'):
                    self.browser.scrol_down(randint(20, 40))

                sleep(uniform(1.0, 2.0))

                # Close the apps menu by clicking elsewhere
                self.browser.find_xpath('//body').click()
                sleep(uniform(1.0, 2.0))

            except Exception as e:
                self.logger.info(f"Could not interact with Google apps menu: {str(e)}")

            # Visit Google Images briefly
            try:
                self.browser.go("https://images.google.com")
                sleep(uniform(2.0, 3.0))

                # Simulate looking at trending images
                if hasattr(self.browser, 'scrol_down'):
                    self.browser.scrol_down(randint(30, 60))

                sleep(uniform(1.5, 2.5))

            except Exception as e:
                self.logger.info(f"Could not visit Google Images: {str(e)}")

        except Exception as e:
            self.logger.error(f"Error in explore_google_services: {str(e)}")

    def _interact_with_gmail(self):
        """Phase 2: Interact with Gmail to establish email usage patterns"""
        self.logger.info("Phase 2: Interacting with Gmail...")

        try:
            # Navigate to Gmail
            self.browser.go("https://mail.google.com")
            sleep(uniform(3.0, 5.0))

            # Wait for Gmail to load with multiple fallback strategies
            gmail_loaded = False
            try:
                # Try to wait for main Gmail interface
                self.browser.wait_xpath_presence('//div[@role="main"]', timeout=15)
                gmail_loaded = True
            except:
                try:
                    # Try alternative Gmail loading indicators
                    self.browser.wait_css_presence('[role="navigation"]', timeout=10)
                    gmail_loaded = True
                except:
                    try:
                        # Try waiting for Gmail logo or header
                        self.browser.wait_xpath_presence('//img[@alt="Gmail"]', timeout=10)
                        gmail_loaded = True
                    except:
                        try:
                            # Try waiting for any Gmail-specific element
                            self.browser.wait_xpath_presence('//div[contains(@class, "gmail")]', timeout=5)
                            gmail_loaded = True
                        except:
                            self.logger.info("Gmail interface not fully loaded, continuing with basic interaction...")

            # Handle potential Gmail welcome screens or popups
            self._handle_gmail_popups()

            # Simulate reading emails (scroll through inbox)
            self.logger.info("Simulating email reading behavior...")
            if hasattr(self.browser, 'scrol_down'):
                scroll_amount = randint(100, 200)
                self.browser.scrol_down(scroll_amount)
            elif hasattr(self.browser, 'human_scroll_page'):
                self.browser.human_scroll_page('down', randint(3, 6))
            else:
                # Fallback scrolling
                try:
                    self.browser.execute_js("window.scrollBy(0, 300);")
                except:
                    pass

            sleep(uniform(2.0, 4.0))

            # Try to interact with Gmail interface elements with modern selectors
            self._interact_with_gmail_compose()
            self._interact_with_gmail_navigation()

        except Exception as e:
            self.logger.error(f"Error in interact_with_gmail: {str(e)}")

    def _handle_gmail_popups(self):
        """Handle Gmail welcome screens, popups, and continue buttons"""
        try:
            # Handle various Gmail popups and continue buttons
            popup_selectors = [
                # Continue buttons in various languages
                '//button[contains(text(), "Continue")]',
                '//button[contains(text(), "Continuer")]',
                '//button[contains(text(), "Continue as")]',
                '//button[contains(text(), "Continuer en tant que")]',
                # Welcome screen buttons
                '//button[contains(text(), "Got it")]',
                '//button[contains(text(), "OK")]',
                '//button[contains(text(), "Compris")]',
                # Close buttons for popups
                '//button[@aria-label="Close"]',
                '//button[@aria-label="Fermer"]',
                # Skip buttons
                '//button[contains(text(), "Skip")]',
                '//button[contains(text(), "Ignorer")]'
            ]

            for selector in popup_selectors:
                try:
                    popup_button = self.browser.find_xpath(selector)
                    if popup_button:
                        self.logger.info(f"Found Gmail popup button: {selector}")
                        if hasattr(self.browser, 'human_click_element'):
                            self.browser.human_click_element(popup_button)
                        else:
                            popup_button.click()
                        sleep(uniform(1.0, 2.0))
                        break
                except:
                    continue

        except Exception as e:
            self.logger.info(f"Could not handle Gmail popups: {str(e)}")

    def _interact_with_gmail_compose(self):
        """Interact with Gmail compose button"""
        try:
            # Modern Gmail compose button selectors
            compose_selectors = [
                '//div[@role="button" and contains(text(), "Compose")]',
                '//div[@role="button" and contains(text(), "Écrire")]',  # French
                '//div[contains(@class, "T-I") and contains(@class, "T-I-KE")]',  # Gmail compose class
                '//div[@gh="cm"]',  # Gmail compose shortcut
                '//div[contains(@aria-label, "Compose")]',
                '//div[contains(@aria-label, "Écrire")]'
            ]

            for selector in compose_selectors:
                try:
                    compose_button = self.browser.find_xpath(selector)
                    if compose_button:
                        self.logger.info(f"Found compose button: {selector}")
                        # Hover over compose button (don't click to avoid opening compose)
                        if hasattr(self.browser, 'human_move_to_element'):
                            self.browser.human_move_to_element(compose_button)
                        sleep(uniform(1.0, 2.0))
                        return
                except:
                    continue

            self.logger.info("Could not find Gmail compose button with any selector")

        except Exception as e:
            self.logger.info(f"Could not interact with compose button: {str(e)}")

    def _interact_with_gmail_navigation(self):
        """Interact with Gmail navigation and labels"""
        try:
            # Modern Gmail navigation selectors
            navigation_actions = [
                # Try to interact with sidebar navigation
                self._click_gmail_sidebar_item,
                # Try to interact with toolbar
                self._interact_with_gmail_toolbar,
                # Try basic scrolling if navigation fails
                self._perform_basic_gmail_scrolling
            ]

            for action in navigation_actions:
                try:
                    if action():
                        break
                except Exception as e:
                    self.logger.info(f"Navigation action failed: {str(e)}")
                    continue

        except Exception as e:
            self.logger.info(f"Could not interact with Gmail navigation: {str(e)}")

    def _click_gmail_sidebar_item(self):
        """Try to click on Gmail sidebar items"""
        try:
            # Modern Gmail sidebar selectors
            sidebar_selectors = [
                # Sent folder
                '//a[contains(@href, "#sent")]',
                '//div[@role="button" and contains(text(), "Sent")]',
                '//div[@role="button" and contains(text(), "Envoyés")]',
                # Drafts folder
                '//a[contains(@href, "#drafts")]',
                '//div[@role="button" and contains(text(), "Drafts")]',
                '//div[@role="button" and contains(text(), "Brouillons")]',
                # Spam folder
                '//a[contains(@href, "#spam")]',
                '//div[@role="button" and contains(text(), "Spam")]',
                # All Mail
                '//a[contains(@href, "#all")]',
                '//div[@role="button" and contains(text(), "All Mail")]',
                '//div[@role="button" and contains(text(), "Tous les messages")]'
            ]

            for selector in sidebar_selectors:
                try:
                    element = self.browser.find_xpath(selector)
                    if element:
                        self.logger.info(f"Clicking Gmail sidebar item: {selector}")
                        if hasattr(self.browser, 'human_click_element'):
                            self.browser.human_click_element(element)
                        else:
                            element.click()

                        sleep(uniform(2.0, 3.0))

                        # Scroll in the selected section
                        if hasattr(self.browser, 'scrol_down'):
                            self.browser.scrol_down(randint(50, 100))
                        elif hasattr(self.browser, 'human_scroll_page'):
                            self.browser.human_scroll_page('down', randint(2, 4))

                        sleep(uniform(1.5, 2.5))
                        return True
                except:
                    continue

            return False

        except Exception as e:
            self.logger.info(f"Could not click Gmail sidebar items: {str(e)}")
            return False

    def _interact_with_gmail_toolbar(self):
        """Interact with Gmail toolbar elements"""
        try:
            # Try to interact with Gmail toolbar buttons (without performing actions)
            toolbar_selectors = [
                '//div[@role="button" and @aria-label]',  # Any toolbar button
                '//div[contains(@class, "T-I")]',  # Gmail toolbar class
                '//div[@gh]'  # Gmail shortcut elements
            ]

            for selector in toolbar_selectors:
                try:
                    elements = self.browser.find_elements_by_xpath(selector)
                    if elements:
                        # Just hover over a random toolbar element
                        element = choice(elements[:3])  # Limit to first 3 to avoid risky elements
                        if hasattr(self.browser, 'human_move_to_element'):
                            self.browser.human_move_to_element(element)
                        sleep(uniform(0.5, 1.0))
                        return True
                except:
                    continue

            return False

        except Exception as e:
            self.logger.info(f"Could not interact with Gmail toolbar: {str(e)}")
            return False

    def _perform_basic_gmail_scrolling(self):
        """Perform basic scrolling in Gmail as fallback"""
        try:
            self.logger.info("Performing basic Gmail scrolling as fallback...")

            # Scroll down
            if hasattr(self.browser, 'scrol_down'):
                self.browser.scrol_down(randint(100, 200))
            elif hasattr(self.browser, 'human_scroll_page'):
                self.browser.human_scroll_page('down', randint(3, 6))
            else:
                self.browser.execute_js("window.scrollBy(0, 400);")

            sleep(uniform(1.0, 2.0))

            # Scroll back up a bit
            if hasattr(self.browser, 'scrol_up'):
                self.browser.scrol_up(randint(50, 100))
            elif hasattr(self.browser, 'human_scroll_page'):
                self.browser.human_scroll_page('up', randint(1, 3))
            else:
                self.browser.execute_js("window.scrollBy(0, -200);")

            sleep(uniform(1.0, 2.0))
            return True

        except Exception as e:
            self.logger.info(f"Basic Gmail scrolling failed: {str(e)}")
            return False

    def _perform_search_activities(self):
        """Phase 3: Perform realistic search activities"""
        self.logger.info("Phase 3: Performing search activities...")

        try:
            # Go back to Google search
            self.browser.go("https://www.google.com")
            sleep(uniform(2.0, 3.0))

            # Perform 2-3 realistic searches
            search_queries = [
                "weather today",
                "news france",
                "restaurants near me",
                "how to cook pasta",
                "best movies 2024",
                "travel destinations europe",
                "healthy recipes",
                "technology news",
                "sports results",
                "online shopping"
            ]

            num_searches = randint(2, 3)
            selected_queries = random.sample(search_queries, num_searches)

            for query in selected_queries:
                try:
                    # Find search box
                    search_box = self.browser.find_xpath('//input[@name="q"]')
                    if search_box:
                        # Clear and type search query
                        search_box.clear()
                        sleep(uniform(0.5, 1.0))

                        if hasattr(self.browser, 'human_type_text'):
                            self.browser.human_type_text(search_box, query, clear_first=False)
                        else:
                            search_box.send_keys(query)

                        sleep(uniform(1.0, 2.0))

                        # Press Enter or click search button
                        search_box.send_keys(Keys.ENTER)
                        sleep(uniform(2.0, 4.0))

                        # Simulate reading search results
                        if hasattr(self.browser, 'scrol_down'):
                            self.browser.scrol_down(randint(100, 200))

                        sleep(uniform(2.0, 3.0))

                        # Sometimes click on a search result
                        if randint(1, 3) == 1:  # 33% chance
                            try:
                                # Find first few search results
                                results = self.browser.find_xpath_all('//h3/parent::a')[:3]
                                if results:
                                    selected_result = choice(results)
                                    if hasattr(self.browser, 'human_click_element'):
                                        self.browser.human_click_element(selected_result)
                                    else:
                                        selected_result.click()

                                    sleep(uniform(3.0, 5.0))

                                    # Simulate reading the page
                                    if hasattr(self.browser, 'scrol_down'):
                                        self.browser.scrol_down(randint(50, 150))

                                    sleep(uniform(2.0, 4.0))

                                    # Go back to search results
                                    self.browser.back()
                                    sleep(uniform(1.5, 2.5))

                            except Exception as e:
                                self.logger.info(f"Could not click on search result: {str(e)}")

                        # Go back to Google homepage for next search
                        if query != selected_queries[-1]:  # Not the last search
                            self.browser.go("https://www.google.com")
                            sleep(uniform(1.5, 2.5))

                except Exception as e:
                    self.logger.info(f"Could not perform search for '{query}': {str(e)}")

        except Exception as e:
            self.logger.error(f"Error in perform_search_activities: {str(e)}")

    def _check_account_settings(self):
        """Phase 4: Check account settings to simulate account management"""
        self.logger.info("Phase 4: Checking account settings...")

        try:
            # Navigate to Google Account settings
            self.browser.go("https://myaccount.google.com")
            sleep(uniform(3.0, 5.0))

            # Simulate browsing account settings
            if hasattr(self.browser, 'scrol_down'):
                self.browser.scrol_down(randint(100, 200))

            sleep(uniform(2.0, 3.0))

            # Try to click on different sections
            try:
                sections = ['Personal info', 'Data & privacy', 'Security']
                for section in sections:
                    try:
                        section_element = self.browser.find_xpath(f'//h2[contains(text(), "{section}")]')
                        if section_element:
                            if hasattr(self.browser, 'human_move_to_element'):
                                self.browser.human_move_to_element(section_element)
                            sleep(uniform(1.0, 2.0))
                            break
                    except:
                        continue

            except Exception as e:
                self.logger.info(f"Could not interact with account sections: {str(e)}")

            # Scroll more to simulate reading
            if hasattr(self.browser, 'scrol_down'):
                self.browser.scrol_down(randint(50, 100))

            sleep(uniform(2.0, 3.0))

        except Exception as e:
            self.logger.error(f"Error in check_account_settings: {str(e)}")

    def _return_to_google_home(self):
        """Phase 5: Return to Google homepage"""
        self.logger.info("Phase 5: Returning to Google homepage...")

        try:
            self.browser.go("https://www.google.com")
            sleep(uniform(2.0, 3.0))

            # Final scroll to simulate one last look
            if hasattr(self.browser, 'scrol_down'):
                self.browser.scrol_down(randint(30, 60))

            sleep(uniform(1.0, 2.0))

        except Exception as e:
            self.logger.error(f"Error in return_to_google_home: {str(e)}")


    def fix_errors(self):
        print("Fixing Errors!!")
        
        while self.browser.running() == True:
            sleep(2.5)



    def check_groups(self):
        self.browser.go("https://groups.google.com/my-groups?hl=fr-FR")
        self.browser.wait_xpath_presence('//span[contains(., "Créer un groupe")]')
        try:
            groups_list = self.browser.find_xpath_all('//div[@data-rowid]/div')
            if groups_list :
                try:
                    self.add_group(self.browser.email, self.get_group_name())
                except:
                    pass
                return True
            else:
                return False
        except:
            return False

    

    def get_group_name(self):
        #self.login_Wait()
        try:
            grp_name = self.browser.execute_js('return document.querySelectorAll("#yDmH0d > c-wiz.zQTmif.SSPGKf.eejsDc > c-wiz > div > div.U3yzR > div > div.ReSblb.OcVpRe.eFM3be > div:nth-child(2) > div:nth-child(1) > div")[0].attributes[2].value')
        except Exception as e:
            self.logger.error(f"JavaScript execution failed: {e}")
            try:
                grp_name = self.browser.find_xpath_all('//div[@data-rowid]/div')[0].get_attribute("data-group-name")
            except Exception as e:
                self.logger.error(f"XPath execution failed: {e}")
                grp_name = None

        return grp_name


    def get_members_num(self):
        try:
            grp_num = self.browser.execute_js('document.querySelectorAll("#yDmH0d > c-wiz:nth-child(19) > div > div.PgZlod > c-wiz > div > div.GeR1W.xaq4Kc > div > html-blob > div > div")[0].textContent')
            grp_num = grp_num.replace(" membres","").replace(" membre","")
        except:
            try:
                grp_num = self.browser.find_css('#yDmH0d > c-wiz:nth-child(19) > div > div.PgZlod > c-wiz > div > div.GeR1W.xaq4Kc > div > html-blob > div > div').text
                grp_num = grp_num.replace(" membres","").replace(" membre","")
            except:
                grp_num = 0
        return grp_num




    def get_members_js(self):
        dig = ''.join(choice(digits) for _ in range(5))
        grp_num = f"grpnum{dig}"
        func = """
        function getnum() {
        mbr = []
        Node_list = document.getElementsByTagName('div')
        for (let i = 0; i < Node_list.length; i++) {
        if (Node_list[i].innerHTML.indexOf("&nbsp;membre") !== -1){
            mbr.push(Node_list[i])
        }
        }
        return  mbr.at(-1).textContent;
        }
        getnum()
        var newDiv = document.createElement("div");
        var newContent = document.createTextNode(getnum());
        newDiv.appendChild(newContent);
        var currentDiv = document.getElementById('div1');
        document.body.insertBefore(newDiv, currentDiv);
        newDiv.setAttribute('id','%s')
        """ %(grp_num)
        self.browser.execute_js(func)
        num = self.browser.find_css(f"#{grp_num}").text
        return num.replace(" membres","").replace(" membre","")



    def accpet_invite(self):
        try:
            self.browser.find_xpath('//div[@aria-label="Ajouter les membres directement. Si vous désactivez cette option, les membres recevront une invitation."]').click()
        except:
            try:
                self.browser.find_xpath('//*[@id="yDmH0d"]/div[4]/div/div[2]/span/c-wiz/div/div[2]/section[1]/span/div[1]/div/div[5]/div[1]').click()
            except:
                try:
                    self.browser.find_css('#yDmH0d > div.NBxL9e.iWO5td > div > div.I7OXgf.PdrZre.ZEeHrd.fNxzgd.VhQQpd.Niudaf.Inn9w.iWO5td > span > c-wiz > div > div.R1f3ob.iiSk4c > section.gXHEcc.VCDHL.qs41qe > span > div:nth-child(2) > div > div.UY6sJb > div.LsSwGf.SWVgue.br5iZc').click()
                except:
                    self.logger.error(f"Can't Find Send Invitations Button!! [Accept_invite]]")



    def pass_members(self):
        captcha_failed = False
        try:
            self.browser.find_xpath('//*[@id="yDmH0d"]/div[4]/div/div[2]/span/c-wiz/div/div[2]/section[1]/span/div[2]/div[2]/span/span').click()
        except:
            try:
                self.browser.execute_js(
                """Array.prototype.slice.call(document.querySelectorAll('span')) .filter(function (el) { return el.textContent === 'Ajouter' })[21].click(); """)
            except:
                self.logger.error(f"Can't Find Send Invitations Button!! [pass_members[1]]")
        
        
        sleep(uniform(1.4,2.2))


        try:
            self.CaptchaSolver()
        except Exception as e:
            self.logger.error(f"+++ Captcha Solver {str(e)} +++")
            captcha_failed = True
            #self.terminate_selenium_driver()
            #return
        
        if captcha_failed == False:
            sleep(uniform(0.5,1.5))
            
            try:
                self.browser.execute_js(
                    """ Array.prototype.slice.call(document.querySelectorAll('span')) .filter(function (el) { return el.textContent === 'Ajouter' })[23].click(); """)
            except:
                try:
                    self.browser.find_xpath('//*[@id="yDmH0d"]/div[4]/div/div[2]/span/c-wiz/div/div[2]/section[2]/span/div/div[2]/div[1]/span/span').click()
                except:
                    try:
                        self.browser.execute_js("Array.prototype.slice.call(document.querySelectorAll('span')) .filter(function (el) { return el.textContent === 'Ajouter' })[23].click();")
                    except:
                        self.logger.error(f"Can't Find Send Invitations Button!! [pass_members[2]]")
                        #self.browser.finish()
                        #self.terminate_selenium_driver()
                        #return
                



    def get_emails(self):
        files = [f for f in os.listdir(data_directory) if os.path.isfile(os.path.join(data_directory, f))]

        files.sort(key=lambda f: int(f.split('_')[1].split('.')[0]))

        with open(os.path.join(data_directory, files[0]), 'r') as file:
            lines = [line.strip() for line in file.readlines()]

        return lines, files[0]
    


    def update_group_list(self, grp_name):
        # Placeholder method - currently not implemented
        _ = grp_name  # Suppress unused parameter warning
        return
    


    def get_group_admins(self, email, group):
        data = {}
        if os.path.exists(map_path):
            with open(map_path, 'r') as f:
                data = json.load(f)

        if email in data:
            for g in data[email]:
                if g["name"] == group:
                    admins = g.get("admins", "No admins found")
                    return admins.split(",") if admins else []

        return None


    def recovery_file(self,file_name,emails):
        with open(os.path.join(data_directory, file_name), 'w') as file:
            for email in emails:
                file.write(f"{email}\n")


    def delete_file(self,file_path):
        if os.path.exists(os.path.join(data_directory,file_path)):
            os.remove(os.path.join(data_directory,file_path))
            self.logger.info(f"### File {file_path} deleted successfully ###")
        else:
            self.logger.error("+++ The file does not exist +++")


    def create_accounts_map(self):
        gmail_accounts_map = []
        if os.path.exists(gmail_map_file):
            with open(gmail_map_file, 'r') as json_file:
                try:
                    existing_data = json.load(json_file)
                    if existing_data and len(existing_data) > 0:
                        # Validate the structure of existing data
                        first_account = existing_data[0]
                        if isinstance(first_account, dict) and 'email' in first_account:
                            self.logger.info("Accounts already exist in gmail_map_file. Skipping generation.")
                            return
                        else:
                            self.logger.warning("Invalid account structure detected. Regenerating accounts map.")
                except json.JSONDecodeError:
                    self.logger.warning("Invalid JSON in gmail_map_file. Regenerating accounts map.")
                    pass


        with open(ua_map, 'r') as ua_file:
            ua_list = json.load(ua_file)
        
        with open(gmail_account_file, 'r') as file:
            for line_num, line in enumerate(file, 1):
                line = line.strip()
                if not line or line.startswith('#'):  # Skip empty lines and comments
                    continue

                try:
                    parts = line.split(':')
                    if len(parts) < 2:
                        parts = line.split(';')

                    if len(parts) >= 2:
                        email = parts[0].strip()
                        password = parts[1].strip()
                        conf = parts[2].strip() if len(parts) >= 3 else ""

                        if not email or not password:
                            self.logger.warning(f"Line {line_num}: Invalid email or password format: {line}")
                            continue

                        gmail_account = {
                            'email': email,
                            'password': password,
                            'ua': choice(ua_list),
                            "email_conf": conf if "@" in conf else "",
                            "phone": conf if "@" not in conf else "",
                            'status': "null"
                        }
                        gmail_accounts_map.append(gmail_account)
                        self.logger.info(f"Added account: {email}")
                    else:
                        self.logger.warning(f"Line {line_num}: Invalid format (expected email:password:recovery): {line}")

                except Exception as e:
                    self.logger.error(f"Line {line_num}: Error parsing account data: {str(e)} - Line: {line}")

        with open(gmail_map_file, 'w') as json_file:
            json.dump(gmail_accounts_map, json_file, indent=4)


    def create_data_parts(self,cmd=None):
        if not os.path.exists(data_directory):
            os.makedirs(data_directory)

        existing_files = [
            f for f in os.listdir(data_directory)
            if os.path.isfile(os.path.join(data_directory, f)) and re.match(r'data_(\d+)\.txt$', f)
        ]

        if cmd is not None:
            if existing_files:
                existing_numbers = [
                    int(re.findall(r'data_(\d+)\.txt', f)[0]) for f in existing_files
                ]
                max_number = max(existing_numbers)
            else:
                max_number = 0

            with open(data_file, 'r') as file:
                lines = file.readlines()

            start_line = max_number * 50
            total_lines = len(lines)

            if start_line < total_lines:
                for i in range(start_line, total_lines, 50):
                    part_number = (i // 50) + 1
                    part_file_path = os.path.join(data_directory, f"data_{part_number}.txt")
                    with open(part_file_path, 'w') as part_file:
                        part_file.writelines(lines[i:i+50])
                self.logger.info(f"Generated data files from {max_number + 1} to {part_number}")
            else:
                self.logger.info("All data files are up to date.")
        else:
            if not existing_files:
                with open(data_file, 'r') as file:
                    lines = file.readlines()
                    for i in range(0, len(lines), 50):
                        part_file_path = os.path.join(data_directory, f"data_{i//50 + 1}.txt")
                        with open(part_file_path, 'w') as part_file:
                            part_file.writelines(lines[i:i+50])
            else:
                self.logger.info("### Data parts already exist. Skipping generation. ###")
                total_files = len(existing_files)
                self.logger.info(f"### Data Files N: {total_files * 50} ###")
            



    def create_groups(self):
        failed = False
        city_name = CityName().name()
        self.grp_name = f"{city_name}{randint(1000000,9999999)}"
        try:
            self.browser.find_xpath("//span[text()='Créer un groupe']").click()
        except:
            try:
                self.browser.find_xpath('//*[@id="yDmH0d"]/c-wiz[1]/div/div/gm-coplanar-drawer/div/div/span/div/div/div/div[1]/div/button').click()
            except:
                try:
                    self.browser.execute_js("""
                        Array.prototype.slice.call(document.querySelectorAll('span')) .filter(function (el) { return el.textContent === 'Créer un groupe' })[0].click();                   
                                    """)
                except:
                    try:
                        self.browser.find_xpath("//button/span[text()='Créer un groupe']").click()
                    except:
                        self.logger.error("Can't Find Create Group Button!!")
                        failed = True
        sleep(1)
        if not failed:
            sleep(1.2)
            #Select regular Groups
            try:
                self.browser.find_xpath("//div[@data-value='googlegroups.com']").click()
            except:
                try:
                    self.browser.execute_js(""" document.querySelector("div[data-value='googlegroups.com']").click(); """)
                except Exception as e:
                    self.logger.error(f"{str(e)}")
            sleep(0.05)

            try:
                self.browser.find_xpath_all("//span[text()='@googlegroups.com']")[1].click()
            except:
                 self.browser.execute_js("""
                    Array.prototype.slice.call(document.querySelectorAll('span'))
                        .filter(function (el) { return el.textContent === '@googlegroups.com' })[1].click();
                """)
            sleep(0.5)
            #* Group Name
            try:
                self.browser.find_css("#yDmH0d > div.NBxL9e.iWO5td > div > div.I7OXgf.PdrZre.ZEeHrd.VhQQpd.Inn9w.iWO5td > span > c-wiz > div > div > div.R1f3ob.iiSk4c > section.gXHEcc.VCDHL.NcKcee.qs41qe > span > div > div.nRiLA > div.X9qMYb > div > div > div > div.n9IS1.oJeWuf > div.FtBNWb > input").send_keys(self.grp_name)
            except:
                try:
                    self.browser.find_xpath("//input[@aria-label='Nom du groupe']").send_keys(self.grp_name)
                except:
                    try:
                        self.browser.execute_js(f""" document.querySelector('[aria-label="Nom du groupe"]').value = "{self.grp_name}")""" )
                        self.browser.execute_js(f""" document.querySelector('[aria-label="Préfixe d\'adresse e-mail du groupe"]').value = "{self.grp_name.lower()}" """)
                    except:
                        try:
                            self.browser.find_xpath('//*[@id="yDmH0d"]/div[4]/div/div[2]/span/c-wiz/div/div/div[2]/section[1]/span/div/div[2]/div[2]/div/div/div/div[1]/div[1]/input').send_keys(self.grp_name)
                        except:
                            pass
            sleep(0.8)
            try:
                self.browser.find_css("#yDmH0d > div.NBxL9e.iWO5td > div > div.I7OXgf.PdrZre.ZEeHrd.VhQQpd.Inn9w.iWO5td > div.OE6hId.J9fJmf > div:nth-child(1) > span > span").click()
            except:
                try:
                    self.browser.execute_js("""
                    Array.prototype.slice.call(document.querySelectorAll('span')) .filter(function (el) { return el.textContent === 'Suivant' })[3].click();                    
                                    """)  
                except:
                    try:
                        self.browser.find_xpath('//*[@id="yDmH0d"]/div[4]/div/div[2]/div[3]/div[1]/span/span').click()
                    except:
                        pass
            sleep(1.3)

            #Publier Les Messages:
            try:
                self.browser.find_css("#yDmH0d > div.NBxL9e.iWO5td > div > div.I7OXgf.PdrZre.ZEeHrd.VhQQpd.Inn9w.iWO5td > span > c-wiz > div > div > div.R1f3ob.iiSk4c > section.gXHEcc.VCDHL.NcKcee.qs41qe > span > div > div.kzQLre > div:nth-child(4) > div.J6Z1mf > div.yEhNJc > div.xk0jZ.AjVdrc.x9Ufpf").click()
            except:
                try:
                    self.browser.find_xpath('//*[@id="yDmH0d"]/div[4]/div/div[2]/span/c-wiz/div/div/div[2]/section[2]/span/div/div[2]/div[4]/div[2]/div[1]/div[2]').click()
                except:
                    pass
            sleep(0.5)

            # Afficher Liste Membres:
            try:
                self.browser.find_css("#yDmH0d > div.NBxL9e.iWO5td > div > div.I7OXgf.PdrZre.ZEeHrd.VhQQpd.Inn9w.iWO5td > span > c-wiz > div > div > div.R1f3ob.iiSk4c > section.gXHEcc.VCDHL.NcKcee.qs41qe > span > div > div.kzQLre > div:nth-child(5) > div.J6Z1mf > div.yEhNJc > div.xk0jZ.AjVdrc.x9Ufpf.qnnXGd").click()
            except:
                try:
                    self.browser.find_xpath('//*[@id="yDmH0d"]/div[4]/div/div[2]/span/c-wiz/div/div/div[2]/section[2]/span/div/div[2]/div[5]/div[2]/div[1]/div[2]').click()
                except:
                    pass

            sleep(0.5)

            try:
                self.browser.find_css("#yDmH0d > div.NBxL9e.iWO5td > div > div.I7OXgf.PdrZre.ZEeHrd.VhQQpd.Inn9w.iWO5td > div.OE6hId.J9fJmf > div:nth-child(4) > span > span").click()
            except:
                try:
                    self.browser.execute_js("""
                    Array.prototype.slice.call(document.querySelectorAll('span')) .filter(function (el) { return el.textContent === 'Suivant' })[3].click();                    
                                    """)
                except:
                    try:
                        self.browser.find_xpath('//*[@id="yDmH0d"]/div[4]/div/div[2]/div[3]/div[1]/span/span').click()
                    except:
                        pass 

            
            sleep(0.7)

            """
            if admins is not None:

                self.accpet_invite()

                sleep(0.7)

                list_admins = admins.split(",")
                for admin in list_admins:
                    sleep(uniform(0.1,0.3))
                    try:
                        self.browser.find_xpath("//input[@aria-label='Gestionnaires du groupe']").send_keys(admin.strip())
                    except:
                        self.browser.find_xpath("(//input[@spellcheck='false'])[2]").send_keys(admin.strip())
                    sleep(uniform(0.1,0.2))
                    try:
                        self.browser.find_xpath("//input[@aria-label='Gestionnaires du groupe']").send_keys(Keys.RETURN)
                    except:
                        self.browser.find_xpath("(//input[@spellcheck='false'])[2]").send_keys(Keys.RETURN)

                sleep(0.5)
            """

            try:
                self.browser.find_css('#yDmH0d > div.NBxL9e.iWO5td > div > div.I7OXgf.PdrZre.ZEeHrd.VhQQpd.Inn9w.iWO5td > div.OE6hId.J9fJmf > div:nth-child(6) > span > span').click()
            except:
                try:
                    self.browser.find_xpath("//div[@role='button'][./span/span[text()='Créer un groupe']])[3]").click()
                except:
                    try:
                        self.browser.execute_js("""
                        Array.prototype.slice.call(document.querySelectorAll('div')) .filter(function (el) { return el.textContent === 'Créer un groupe' })[4].click()                   
                                        """)
                    except:
                        try:
                            self.browser.find_xpath('//*[@id="yDmH0d"]/div[4]/div/div[2]/div[3]/div[6]/span/span').click()
                        except:
                            self.browser.finish()
                            self.terminate_selenium_driver()
                            self.logger.error(f"Can't Find Create Group Button!!")
                            return


            sleep(2)
            try:
                self.CaptchaSolver()
            except Exception as e:
                self.logger.error(f"{str(e)}")
                #self.terminate_selenium_driver()


            sleep(1.5)

            try:
                self.browser.execute_js("""
                Array.prototype.slice.call(document.querySelectorAll('span')) .filter(function (el) { return el.textContent === 'Créer un groupe' })[3].click();""")
            except:
                try:
                    self.browser.find_xpath("//div[@aria-label='Créer un groupe'][./span/span[text()='Créer un groupe']]").click()
                except:
                    self.logger.error(f"Can't Find Create Group Button After Captcha!!")
                    #self.browser.finish()
                    #self.terminate_selenium_driver()

            sleep(1.5)

            retry_count = 0
            max_retries = 3

            while retry_count < max_retries:
                sleep(uniform(5.5, 7.5))
                if self.check_groups():
                    self.add_group(self.browser.email, self.grp_name.lower())
                    self.logger.info(f"Group {self.grp_name} Created Successfully!!")
                    break
                self.logger.info(f"Retry {retry_count + 1}: Group Creation Failed!!")
                retry_count += 1

                if retry_count == max_retries:
                    self.logger.error(f"Failed to create group after {max_retries} retries.")
                    with open("DeadAccounts.txt", "a") as file:
                        file.write(f"{self.browser.email}\n")
            



    def upload(self):
        self.grp_name = self.get_groups(self.browser.email)[0]
        self.grp_url =  f"https://groups.google.com/g/{self.grp_name}"
        self.browser.go(self.grp_url + "/members?hl=fr-FR")
        self.uploaded_data = []
        #self.i = 0
        error_404 = self.check_js("L'URL demandée est introuvable sur ce serveur. C'est tout")
        error_404 = False
        if error_404 is True:
            self.remove_group(self.browser.email,self.grp_name)
        else:
            try:
                self.first_members_num = self.get_members_js()
                self.update_group_members(self.browser.email,self.grp_name.lower(),self.first_members_num)
            except:
                try:
                    self.first_members_num = self.get_members_num()
                    self.update_group_members(self.browser.email,self.grp_name.lower(),self.first_members_num)
                except:
                    self.first_members_num = 0
            self.logger.info(f"### Group Members: {self.first_members_num} ###")

            try:
                self.emails, file_name = self.get_emails()
            except Exception as e:
                self.logger.error(f"{str(e)}")
                self.browser.finish()
                self.terminate_selenium_driver()
                self.logger.error("+++ Can't get data!! +++")
                return
            
            try:
                self.browser.wait_xpath_presence('//div[@aria-label="Ajouter"]/span/span[contains(., "Ajouter")]')
            except:
                self.browser.wait_css_clickable("#yDmH0d > c-wiz:nth-child(19) > div > div.PgZlod > c-wiz > div > div.GeR1W.xaq4Kc > div > html-blob > div > span > div:nth-child(1) > span > span")

            try:
                self.browser.find_xpath('//div[@aria-label="Ajouter"]/span/span[contains(., "Ajouter")]').click()
            except Exception as e:
                self.logger.error(f"{str(e)}")
                return

            sleep(uniform(0.5,1.5))


            try:
                self.browser.find_xpath('//div[@aria-label="Ajouter les membres directement. Si vous désactivez cette option, les membres recevront une invitation."]').click()
            except:
                try:
                    self.browser.find_xpath('//*[@id="yDmH0d"]/div[4]/div/div[2]/span/c-wiz/div/div[2]/section[1]/span/div[1]/div/div[5]/div[1]').click()
                except:
                    try:
                        self.browser.find_css('#yDmH0d > div.NBxL9e.iWO5td > div > div.I7OXgf.PdrZre.ZEeHrd.fNxzgd.VhQQpd.Niudaf.Inn9w.iWO5td > span > c-wiz > div > div.R1f3ob.iiSk4c > section.gXHEcc.VCDHL.qs41qe > span > div:nth-child(2) > div > div.UY6sJb > div.LsSwGf.SWVgue.br5iZc').click()
                    except:
                        self.logger.error(f"+++ Can't Find Ajouter Directement Button!! +++")
                        
            self.logger.info(f"### Uploading  {len(self.emails)} emails to Group: {self.grp_name} ###")
            for email in self.emails:
                sleep(uniform(0.1,0.3))
                try:
                    input_element = self.browser.find_xpath("//input[@aria-label='Membres du groupe']")
                    if hasattr(self.browser, 'human_type_text'):
                        self.browser.human_type_text(input_element, email, clear_first=False)
                    else:
                        input_element.send_keys(email)
                except:
                    input_element = self.browser.find_xpath("//input[@spellcheck='false']")
                    if hasattr(self.browser, 'human_type_text'):
                        self.browser.human_type_text(input_element, email, clear_first=False)
                    else:
                        input_element.send_keys(email)
                self.uploaded_data.append(email)
                sleep(uniform(0.01,0.05))
                try:
                    self.browser.find_xpath("//input[@aria-label='Membres du groupe']").send_keys(Keys.RETURN)
                except:
                    self.browser.find_xpath("//input[@spellcheck='false']").send_keys(Keys.RETURN)
                    
            self.pass_members()

            sleep(uniform(8.5,9.5))

            quota_reached = self.check_js("Échec de l'ajout des membres, car vous avez dépassé votre limite quotidienne. Veuillez réessayer plus tard.")
            self.logger.info(f"### Quota Reached: {quota_reached} ###")

            self.uploaded_sum = len(self.uploaded_data)
            retry_count = 0
            max_retries = 3

            if quota_reached == False:
                sleep(uniform(10.5, 11.5))
                while retry_count < max_retries:
                    # Use enhanced driver's refresh method or fallback to standard refresh
                    if hasattr(self.browser, 'refresh'):
                        self.browser.refresh()
                    else:
                        self.browser.browser.refresh()  # Fallback to underlying browser
                    sleep(uniform(5.5, 7.5))
                    try:
                        self.finish_num_mem = self.get_members_js()
                    except:
                        try:
                            self.finish_num_mem = self.get_members_num()
                        except:
                            self.finish_num_mem = "0"
                    self.logger.info(f"Retry {retry_count + 1}: finish_num_mem = {self.finish_num_mem}, first_members_num = {self.first_members_num}")
                    if int(self.finish_num_mem) > int(self.first_members_num):
                        break
                    retry_count += 1
            else:
                sleep(uniform(2.5, 3.5))

            self.logger.info(f"### Group Members: {self.finish_num_mem} ###")


            if int(self.finish_num_mem) > int(self.first_members_num):
                try:
                    self.delete_file(file_name)
                    self.logger.info(f"### Data Uploaded Successfully!! ###")
                except Exception as e:
                    self.logger.error(f"{str(e)}")

            elif int(self.first_members_num) >= int(self.finish_num_mem) or quota_reached:
                self.logger.error("+++ Data Not Uploaded!! +++")
                return
            else:
                return
            self.update_group_members(self.browser.email,self.grp_name.lower(),self.finish_num_mem)
            
        sleep(2.5)



    def run(self):
        need_fix = []
        with open(gmail_map_file, 'r') as file:
            jsn_data = json.load(file)
        if len(jsn_data) == 0:
            self.logger.info("+++ No Accounts Found +++")
        
        if "fix_errors" in self.actions:
            for account in jsn_data:
                email = account["email"]
                with open(map_path, 'r') as f:
                    grp_data = json.load(f)
                    if email not in grp_data:
                        need_fix.append(email)
                    
        self.logger.info(f"### Total Accounts: {len(jsn_data)} ###")
        self.ac = 0
        for account in jsn_data:
            self.ac+=1
            print("\n")
            self.logger.info(f"====== Account : {self.ac} ======")
            self.email = account["email"]
            self.password = account["password"]
            ua_agent = account["ua"]
            status = account["status"]

            try:
                if "reload_profiles" in self.actions:
                    self.remove_profile(self.email)
                self.browser = Driver(self.email,self.password,ua_agent,self.ac)
                try:
                    self.browser.go("https://accounts.google.com/signin")
                    if "reload_profiles" in self.actions:
                        self.browser.go("https://google.com")
                        self.logger.info(f"### Profile: {self.browser.email} ###")
                        try:
                            cookies_path = f"{home}/Cookies/{self.browser.email}/cookies.json"
                            with open(cookies_path, 'r') as f:
                                cookies = json.load(f)

                            restored_count = 0
                            failed_count = 0
                            host_prefix_failed = 0

                            for cookie in cookies:
                                try:
                                    cookie_name = cookie.get('name', 'unknown')
                                    is_host_prefix = cookie_name.startswith('__Host-')

                                    self.browser.add_cookie(cookie)
                                    restored_count += 1

                                except Exception as e:
                                    failed_count += 1
                                    error_msg = str(e).lower()

                                    if is_host_prefix:
                                        host_prefix_failed += 1
                                        if 'invalid cookie domain' in error_msg or 'unable to set cookie' in error_msg:
                                            self.logger.debug(f"Expected error for __Host- cookie {cookie_name}: Chrome security restrictions")
                                        else:
                                            self.logger.info(f"__Host- cookie {cookie_name} failed: {e}")
                                    elif 'secure' in error_msg and 'http' in error_msg:
                                        self.logger.debug(f"Secure cookie {cookie_name} cannot be set on HTTP - expected")
                                    else:
                                        self.logger.warning(f"Error restoring cookie {cookie_name}: {e}")

                            self.logger.info(f"Cookie restoration: {restored_count}/{len(cookies)} successful, {failed_count} failed ({host_prefix_failed} __Host- expected failures)")

                        except Exception as cookie_error:
                            self.logger.warning(f"Error loading cookies file: {cookie_error}")

                            local_storage_path = f"{home}/Cookies/{self.browser.email}/localStorage.json"

                            with open(local_storage_path, 'r') as f:
                                local_storage = json.load(f)
                                if local_storage:
                                    for key, value in local_storage.items():
                                        self.browser.execute_js(f"window.localStorage.setItem('{key}', '{value}');")
                            self.logger.info(f"### Profile Reloaded: {self.browser.email} ###")

                            # For human_actions mode (option 9), always perform human actions even with cookies
                            if "human_actions" in self.actions:
                                self.logger.info("### Performing Human Actions After Cookie Reload (Option 9) ###")
                                self.perform_human_actions_after_login()
                                self.export_ck_lc()
                                self.update_email_status(self.browser.email, "active")
                                self.logger.info(f"### Human Actions Completed for {self.browser.email} ###")
                                continue  # Skip to next account

                        except FileNotFoundError as e:
                            self.logger.error(f"+++ File not found: {e.filename} +++")
                        except json.JSONDecodeError as e:
                            self.logger.error(f"+++ Invalid JSON in file: {e.msg} +++")
                        except Exception as e :
                            self.logger.error(f"{str(e)}")
                        
                except Exception as e:
                    if "ERR_PROXY_CONNECTION_FAILED" in str(e) or "net" in str(e) or "ERR_CONNECTION_RESET" in str(e) :
                        self.logger.error(f"{str(e)}")
                        break
                sleep(1)
                current_url = self.browser.this_url()
                self.logger.info(f"DEBUG: Current URL after navigation: {current_url}")
                self.logger.info(f"DEBUG: Actions passed to script: {self.actions}")
                self.logger.info(f"DEBUG: Account status: {status}")

                # Check if already logged in and handle human actions for option 9
                if "https://myaccount.google.com/?utm_source=sign_in_no_continue" in current_url:
                    self.logger.info("### Account already logged in successfully ###")
                    if "human_actions" in self.actions:
                        self.logger.info("### Performing Human Actions (Already Logged In) ###")
                        self.perform_human_actions_after_login()
                        self.export_ck_lc()
                        self.update_email_status(self.browser.email, "active")
                        self.logger.info(f"### Human Actions Completed for {self.browser.email} ###")
                        continue  # Skip to next account

                if "https://myaccount.google.com/?utm_source=sign_in_no_continue" not in current_url or "fix_errors" in self.actions:
                    if "fix_errors" not in self.actions:
                        if status == "active" or status == "null" or status == "phone_verification_required":
                            """
                            if "?hl=fr-FR" not in self.browser.this_url():
                                self.browser.go(f"{self.browser.this_url()}?hl=fr-FR")
                            """

                            if "signinchooser" in self.browser.this_url():
                                self.signchooser()


                            if "signin/confirmidentifier" in self.browser.this_url():
                                self.webreauth()
                            

                            elif "accounts.google.com/v3/signin/identifier" in self.browser.this_url():
                                self.logger.info(f"On Google sign-in page. Actions: {self.actions}")
                                if "login" in self.actions:
                                    self.logger.info("Login action found - proceeding with login")
                                    self.login()
                                else:
                                    self.logger.warning(f"Login action not found in actions: {self.actions}")
                                    self.logger.warning("Skipping login - add 'login' to actions to enable login")
                                    self.logger.warning("FORCING WAIT: Browser will stay open until manual intervention")
                                    self.logger.warning("To proceed: Add 'login' to your actions or manually complete the login")

                                    # Force wait to prevent browser closure
                                    while True:
                                        try:
                                            current_url = self.browser.this_url()
                                            if "accounts.google.com" not in current_url:
                                                self.logger.info("User navigated away from sign-in page - continuing")
                                                break
                                            sleep(5)  # Check every 5 seconds
                                        except:
                                            self.logger.info("Browser closed by user - exiting wait loop")
                                            break

                                # Always perform human actions after successful login (unless explicitly disabled)
                                if "skip_human_actions" not in self.actions:
                                    self.logger.info("### Performing Human Actions After Login ###")
                                    self.perform_human_actions_after_login()

                                # Handle different action modes
                                if "human_actions" in self.actions:
                                    # For human_actions mode, we only do login + human actions, then stop
                                    self.export_ck_lc()
                                    self.update_email_status(self.browser.email, "active")
                                    self.logger.info(f"### Human Actions Completed for {self.browser.email} ###")
                                    continue  # Skip to next account

                                self.export_ck_lc()
                                self.update_email_status(self.browser.email, "active")

                            if "Login&Wait" in self.actions:
                                self.login_Wait()

                            elif "human_actions" not in self.actions:  # Skip group operations for human_actions mode
                                if self.check_groups() == False:
                                    self.create_groups()
                                else:
                                    self.logger.info(f"Group Already Exist : {self.get_group_name()}")
                                try:
                                    self.upload()
                                except Exception as e:
                                    self.logger.error(f"Upload '1' {str(e)}")
                                

                    else:
                        if self.email in need_fix:
                            self.logger.info(f"+++ Fixing Errors for {self.browser.email} +++")
                            self.fix_errors()
                            #self.update_email_status(self.browser.email, "active")

                        # Always perform human actions if in human_actions mode, even for fix_errors
                        if "human_actions" in self.actions:
                            self.logger.info("### Performing Human Actions (Fix Errors Mode) ###")
                            self.perform_human_actions_after_login()
                            self.export_ck_lc()
                            self.update_email_status(self.browser.email, "active")
                            self.logger.info(f"### Human Actions Completed for {self.browser.email} ###")

                else:
                    # Only do group operations if NOT in human_actions mode
                    if "human_actions" not in self.actions:
                        if self.check_groups() == False:
                            try:
                                self.create_groups()
                            except Exception as e:
                                self.browser.logger.error(str(e))
                        else:
                            self.logger.info(f"Group Already Exist : {self.get_group_name()}")
                        try:
                            self.upload()
                        except Exception as e:
                            self.logger.error(f"Upload '2' {str(e)}")
                    else:
                        self.logger.info("### Human Actions Mode - Skipping group operations ###")
                        # Perform human actions even if we're in the else block
                        self.logger.info("### Performing Human Actions ###")
                        self.perform_human_actions_after_login()
                        self.export_ck_lc()
                        self.update_email_status(self.browser.email, "active")
                        self.logger.info(f"### Human Actions Completed for {self.browser.email} ###")


                # Safety check: Don't close browser if still on sign-in page
                current_url = self.browser.this_url()
                self.logger.info(f"DEBUG: Browser running status: {hasattr(self.browser, 'running') and self.browser.running()}")
                self.logger.info(f"DEBUG: Browser object exists: {hasattr(self, 'browser') and self.browser is not None}")

                if any(pattern in current_url for pattern in ['signin', 'accounts.google.com']):
                    self.logger.warning(f"Still on sign-in page: {current_url}")
                    self.logger.warning("Not closing browser - manual intervention may be needed")
                    self.logger.warning("Check if 'login' action is included in the script execution")
                    self.logger.info("DEBUG: Skipping browser.finish() call due to sign-in page detection")
                else:
                    self.logger.info("DEBUG: Calling browser.finish() - not on sign-in page")
                    self.browser.finish()
                    self.terminate_selenium_driver()


            except Exception as e:
                self.logger.error(f"{str(e)}")
                try:
                    # Safety check: Don't close browser if still on sign-in page
                    current_url = self.browser.this_url()
                    if any(pattern in current_url for pattern in ['signin', 'accounts.google.com']):
                        self.logger.warning(f"Exception occurred but still on sign-in page: {current_url}")
                        self.logger.warning("Not closing browser due to exception - manual intervention may be needed")
                    else:
                        self.browser.finish()
                        self.terminate_selenium_driver()
                except Exception as e:
                    pass
                    
        try:
            # Final safety check: Don't close browser if still on sign-in page
            current_url = self.browser.this_url()
            self.logger.info(f"DEBUG: Final cleanup - Browser running: {hasattr(self.browser, 'running') and self.browser.running()}")
            self.logger.info(f"DEBUG: Final cleanup - Current URL: {current_url}")

            if any(pattern in current_url for pattern in ['signin', 'accounts.google.com']):
                self.logger.warning(f"Final cleanup: Still on sign-in page: {current_url}")
                self.logger.warning("Final cleanup: Not closing browser - manual intervention may be needed")
                self.logger.warning("Final cleanup: Check if 'login' action is included in the script execution")
                self.logger.info("DEBUG: Final cleanup - Skipping browser.finish() due to sign-in page")
            else:
                self.logger.info("DEBUG: Final cleanup - Calling browser.finish()")
                self.browser.finish()
                self.terminate_selenium_driver()
        except Exception as e:
            self.logger.error(f"DEBUG: Final cleanup exception: {str(e)}")
            pass



class Main():
    def __init__(self) -> None:
        logging.basicConfig(
            level=logging.INFO,  
            format='[%(asctime)s - %(levelname)s - %(message)s]',
            datefmt='%Y-%m-%d'
        )
        self.logger = logging.getLogger("Main")
        self.logger.info(f"Starting Groups App on User : {os.getlogin()} !!")

        start_time = time.time()

        balance = self.get_balance()
        if balance == "-0.0":
            print("Captcha out of Balance")
        else:
            while True:
                if len(sys.argv) > 1:
                    answ = sys.argv[1]
                else:
                    answ = self.questions()
                if answ == "1":
                    actions = ["login", "create_groups", "upload"]
                    break
                elif answ == "2":
                    actions = ["fix_errors"]
                    break
                elif answ == "3":
                    actions = ["reload_profiles"]
                    break
                elif answ == "4":
                    self.logger.info(f"### Regenerate Data ###")
                    self.create_data_parts("regenerate")
                    print("Press any key to continue...")
                    msvcrt.getch()
                elif answ == "5":
                    self.logger.info(f"### Groups Count : {str(self.count_groups())} ###")
                    print("Press any key to continue...")
                    msvcrt.getch()
                    os.system('cls' if os.name == 'nt' else 'clear')
                elif answ == "6":
                    self.logger.info(f"### Members Count : {str(self.count_members())} ###")
                    print("Press any key to continue...")
                    msvcrt.getch()
                    os.system('cls' if os.name == 'nt' else 'clear')
                elif answ == "7":
                    self.logger.info(f"### {os.getlogin()} Data Count : {str(self.count_data_files())} ###")
                    self.logger.info(f"### {os.getlogin()} Data Files Count : {str(int(self.count_data_files()/50))} ###")
                    return
                elif answ == "8":
                    self.logger.info(f"### Exporting Accounts ###")
                    self.process_dead_accounts()
                    self.logger.info("### Exporting Accounts Done!! ### ")
                    os.system('cls' if os.name == 'nt' else 'clear')
                elif answ == "9":
                    actions = ["login", "human_actions"]
                    break
                else:
                    self.logger.info("Invalid choice, please try again.")
                    if len(sys.argv) > 1:
                        sys.exit(1)

        
        self.create_data_parts()
        worker = Worker(actions)
        worker.create_accounts_map()
        worker.run()

        end_time = time.time()
        execution_time = end_time - start_time
        execution_time_hours = execution_time / 3600
        self.logger.info(f"Script execution time: {execution_time_hours:.2f} hours")



    def get_balance(self):
        api_key = "d315b270071ccc3922a75b7c56e72da1"
        solver = TwoCaptcha(api_key)
        try:
            balance = solver.balance()
            balance = float(str(balance)[:4])  
        except Exception as e:
            self.logger.error(f"{str(e)}")
            balance = 0

        return balance

    def count_groups(self):
        with open(map_path, 'r') as file:
            data = json.load(file)
        name_count = 0
        for _, groups in data.items():
            for group in groups:
                if 'name' in group:
                    name_count += 1
        return name_count
    

    def count_data_files(self):
        num_files = len([
            f for f in os.listdir(data_directory)
            if os.path.isfile(os.path.join(data_directory, f))
        ])
        return num_files * 50


    def create_data_parts(self, cmd=None):
        if not os.path.exists(data_directory):
            os.makedirs(data_directory)
    
        existing_files = [
            f for f in os.listdir(data_directory)
            if os.path.isfile(os.path.join(data_directory, f)) and re.match(r'data_(\d+)\.txt$', f)
        ]

        existing_numbers = set(
            int(re.findall(r'data_(\d+)\.txt', f)[0]) for f in existing_files
        )
    
        if cmd is not None:
            if existing_numbers:
                max_number = max(existing_numbers)
            else:
                max_number = 0
    
            with open(data_file, 'r') as file:
                lines = file.readlines()
    
            total_lines = len(lines)
    
            part_number = max_number + 1
            for i in range(0, total_lines, 50):
                part_file_path = os.path.join(data_directory, f"data_{part_number}.txt")
                with open(part_file_path, 'w') as part_file:
                    part_file.writelines(lines[i:i+50])
                part_number += 1
    
            self.logger.info(f"Generated data files from {max_number + 1} to {part_number - 1}")
        else:
            if not existing_files:
                with open(data_file, 'r') as file:
                    lines = file.readlines()
                    for i in range(0, len(lines), 50):
                        part_file_path = os.path.join(data_directory, f"data_{(i // 50) + 1}.txt")
                        with open(part_file_path, 'w') as part_file:
                            part_file.writelines(lines[i:i+50])
                self.logger.info("Generated data parts.")
            else:
                self.logger.info("### Data parts already exist. Skipping generation. ###")
                total_files = len(existing_files)
                self.logger.info(f"### Data Files N: {total_files * 50} ###")



    def count_members(self):
        with open(map_path, 'r') as file:
            data = json.load(file)
        
        total_members = 0
        for _, groups in data.items():
            for group in groups:
                if 'members_num' in group:
                    total_members += int(group['members_num'])
    
        return total_members


    def clean_file(self, email):
        """Clean profile files for specific email - Enhanced with profile manager support"""
        try:
            # Try to use enhanced driver's deep clean if available
            if hasattr(self, 'browser') and hasattr(self.browser, 'deep_clean_profile'):
                self.browser.deep_clean_profile(email)
                self.logger.info(f"Deep clean completed using enhanced driver for {email}")
            elif hasattr(self, 'browser') and hasattr(self.browser, 'remove_profile'):
                self.browser.remove_profile(email)
                self.logger.info(f"Profile cleaned using enhanced driver for {email}")
            else:
                # Fallback to direct file removal
                profile = f"{profile_home}/{email}"
                if os.path.exists(profile):
                    shutil.rmtree(profile)
                    self.logger.info(f"Profile {profile} removed successfully.")
                else:
                    self.logger.warning(f"Profile {profile} does not exist.")
        except Exception as e:
            self.logger.error(f"Error removing profile for {email}: {str(e)}")

    def deep_clean_profile(self, email):
        """
        Perform deep clean of profile to resolve cookie mismatch issues

        Args:
            email (str): Email to deep clean profile for
        """
        try:
            if hasattr(self, 'browser') and hasattr(self.browser, 'deep_clean_profile'):
                return self.browser.deep_clean_profile(email)
            else:
                # Fallback to basic clean
                self.clean_file(email)
                return True
        except Exception as e:
            self.logger.error(f"Error during deep clean for {email}: {str(e)}")
            return False

    def force_clean_all_profiles(self):
        """
        Force clean all profiles - USE WITH CAUTION!
        This will remove all profile data and may resolve persistent cookie issues
        """
        try:
            if hasattr(self, 'browser') and hasattr(self.browser, 'force_clean_all_profiles'):
                return self.browser.force_clean_all_profiles()
            else:
                # Fallback to removing profile directory
                if os.path.exists(profile_home):
                    shutil.rmtree(profile_home)
                    os.makedirs(profile_home, exist_ok=True)
                    self.logger.info("Force cleaned all profiles (fallback method)")
                    return True
                return False
        except Exception as e:
            self.logger.error(f"Error during force clean: {str(e)}")
            return False

    def show_profile_locations(self):
        """Show where profiles are stored for debugging"""
        try:
            self.logger.info("=== PROFILE STORAGE LOCATIONS ===")
            self.logger.info(f"Main profiles directory: {profile_home}")
            self.logger.info(f"Profiles directory exists: {os.path.exists(profile_home)}")

            # Show cookies directory
            cookies_base = f"{home}/Cookies"
            self.logger.info(f"Legacy cookies directory: {cookies_base}")
            self.logger.info(f"Cookies directory exists: {os.path.exists(cookies_base)}")

            # List existing profiles
            if os.path.exists(profile_home):
                profiles = [d for d in os.listdir(profile_home) if os.path.isdir(os.path.join(profile_home, d))]
                self.logger.info(f"Existing profiles: {len(profiles)}")
                for profile in profiles[:5]:  # Show first 5
                    self.logger.info(f"  - {profile}")
                if len(profiles) > 5:
                    self.logger.info(f"  ... and {len(profiles) - 5} more")

            # Show current profile if available
            if hasattr(self, 'email') and self.email:
                current_profile_path = f"{profile_home}/{self.email}"
                self.logger.info(f"Current profile ({self.email}): {current_profile_path}")
                self.logger.info(f"Current profile exists: {os.path.exists(current_profile_path)}")

            self.logger.info("=== END PROFILE LOCATIONS ===")

        except Exception as e:
            self.logger.error(f"Error showing profile locations: {e}")

    def process_dead_accounts(self):
        with open(dead_accounts, 'r') as file:
            dead_emails = {line.strip() for line in file if line.strip()}
        
        with open(gmail_map_file, 'r') as file:
            gmail_accounts_map = json.load(file)

        matching_accounts = []

        if isinstance(gmail_accounts_map, list):
            updated_accounts_map = []
            for account in gmail_accounts_map:
                email = account.get('email')
                if email in dead_emails:
                    matching_accounts.append(f"{email}:{account.get('password')}")
                    self.clean_file(email)
                else:
                    updated_accounts_map.append(account)
            gmail_accounts_map = updated_accounts_map
        elif isinstance(gmail_accounts_map, dict):
            for email, account_info in gmail_accounts_map.items():
                if email in dead_emails:
                    matching_accounts.append(f"{email}:{account_info['password']}")
            gmail_accounts_map = {email: info for email, info in gmail_accounts_map.items() if email not in dead_emails}
        else:
            raise ValueError("Unexpected JSON structure in Gmail File")

        with open(gmail_map_file, 'w') as file:
            json.dump(gmail_accounts_map, file, indent=4)

        with open("recycled_accounts.txt", 'w') as file:
            for account in matching_accounts:
                file.write(f"{account}\n")

        with open(dead_accounts, 'w') as file:
            file.truncate()


    def questions(self):
        if not os.path.exists(settings_path):
            use_proxy = input("settings.json not found. Do you want to use a proxy? (yes/no): ").strip().lower()
            if use_proxy.lower() in ['y', 'yes']:
                if os.path.exists(proxy_file):
                    with open(proxy_file, 'r') as prx_file:
                        proxies = prx_file.readlines()
                    if proxies:
                        proxy = random.choice(proxies).strip()
                        settings = {
                            'use_proxy': True,
                            'proxy': proxy
                        }
                        with open(settings_path, 'w') as settings_file:
                            json.dump(settings, settings_file, indent=4)
                        self.logger.info("Proxy settings saved to settings.json")
                    else:
                        self.logger.error("proxy.txt is empty.")
                else:
                    self.logger.error("proxy.txt not found.")
            else:
                settings = {
                    'use_proxy': False
                }
                with open(settings_path, 'w') as settings_file:
                    json.dump(settings, settings_file, indent=4)
                self.logger.info("Settings saved to settings.json without proxy")

        self.logger.info("### What do you want to do? ###")
        self.logger.info(""" 1. Login & Create _ Upload""")
        self.logger.info(""" 2. Fix Errors : Like change Phone / Update Settings""")
        self.logger.info(""" 3. Reload Profiles""")
        self.logger.info(""" 4. Regenarate Data Parts""")
        self.logger.info(""" 5. Count Groups Number""")
        self.logger.info(""" 6. Count Group Members""")
        self.logger.info(""" 7. Count Data Files""")
        self.logger.info(""" 8. Export DeadAccounts""")
        self.logger.info(""" 9. Login with Human Actions (Account Warming)""")
        return input("Please enter your choice: ")

Main()




    


    

    

    


